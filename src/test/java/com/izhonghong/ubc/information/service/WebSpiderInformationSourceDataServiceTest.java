package com.izhonghong.ubc.information.service;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.service.impl.abstracts.WebSpiderInformationSourceDataServiceImpl;
import com.izhonghong.ubc.information.util.InformationSourceRequestBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.management.openmbean.InvalidKeyException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSpiderInformationSourceDataService 测试类
 */
@ExtendWith(MockitoExtension.class)
public class WebSpiderInformationSourceDataServiceTest {

    private WebSpiderInformationSourceDataServiceImpl service;
    private InformationSourceBean informationSourceBean;

    @BeforeEach
    void setUp() {
        service = new WebSpiderInformationSourceDataServiceImpl();
        informationSourceBean = new InformationSourceBean();
        informationSourceBean.setServiceUrl("http://test-api-server/api/search");
    }

    @Test
    void testSearchInformationSourceWithValidRequestBody() {
        // 构建有效的请求体
        JSONObject condition = InformationSourceRequestBuilder.condition()
                .k3Id("test123")
                .k3IdName("测试信息源")
                .organizer("测试主办单位")
                .name("测试账号")
                .mediaLevel("央级")
                .verifyType("政务")
                .status(0)
                .build();

        JSONObject paginator = InformationSourceRequestBuilder.paginator(0, 100)
                .sorts("created_at", "desc")
                .build();

        JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(condition, paginator);

        // 验证请求体结构正确
        assertNotNull(requestBody);
        assertTrue(requestBody.containsKey("condition"));
        assertTrue(requestBody.containsKey("paginator"));
        
        JSONObject conditionResult = requestBody.getJSONObject("condition");
        JSONObject paginatorResult = requestBody.getJSONObject("paginator");
        
        assertNotNull(conditionResult);
        assertNotNull(paginatorResult);
        assertEquals("test123", conditionResult.getString("k3Id"));
        assertEquals("测试信息源", conditionResult.getString("k3IdName"));
        assertEquals(0, paginatorResult.getIntValue("from"));
        assertEquals(100, paginatorResult.getIntValue("size"));
    }

    @Test
    void testValidateRequestBodyWithNullRequestBody() {
        // 测试空请求体
        InvalidKeyException exception = assertThrows(InvalidKeyException.class, () -> {
            service.searchInformationSource(informationSourceBean, null);
        });
        assertEquals("请求体不能为空", exception.getMessage());
    }

    @Test
    void testValidateRequestBodyWithMissingCondition() {
        // 测试缺少condition字段
        JSONObject requestBody = new JSONObject();
        JSONObject paginator = new JSONObject();
        paginator.put("from", 0);
        paginator.put("size", 100);
        requestBody.put("paginator", paginator);

        InvalidKeyException exception = assertThrows(InvalidKeyException.class, () -> {
            service.searchInformationSource(informationSourceBean, requestBody);
        });
        assertEquals("请求体必须包含condition字段", exception.getMessage());
    }

    @Test
    void testValidateRequestBodyWithMissingPaginator() {
        // 测试缺少paginator字段
        JSONObject requestBody = new JSONObject();
        JSONObject condition = new JSONObject();
        requestBody.put("condition", condition);

        InvalidKeyException exception = assertThrows(InvalidKeyException.class, () -> {
            service.searchInformationSource(informationSourceBean, requestBody);
        });
        assertEquals("请求体必须包含paginator字段", exception.getMessage());
    }

    @Test
    void testValidateRequestBodyWithMissingPaginatorFields() {
        // 测试paginator缺少必需字段
        JSONObject requestBody = new JSONObject();
        JSONObject condition = new JSONObject();
        JSONObject paginator = new JSONObject();
        // 缺少from和size字段
        
        requestBody.put("condition", condition);
        requestBody.put("paginator", paginator);

        InvalidKeyException exception = assertThrows(InvalidKeyException.class, () -> {
            service.searchInformationSource(informationSourceBean, requestBody);
        });
        assertEquals("paginator必须包含from和size字段", exception.getMessage());
    }

    @Test
    void testValidateRequestBodyWithEmptyServiceUrl() {
        // 测试空serviceUrl
        InformationSourceBean emptyUrlBean = new InformationSourceBean();
        // serviceUrl为空
        
        JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(
                InformationSourceRequestBuilder.condition().build(),
                InformationSourceRequestBuilder.paginator(0, 10).build()
        );

        InvalidKeyException exception = assertThrows(InvalidKeyException.class, () -> {
            service.searchInformationSource(emptyUrlBean, requestBody);
        });
        assertEquals(" Missing key parameters ", exception.getMessage());
    }

    @Test
    void testConditionBuilderWithAllFields() {
        // 测试条件构建器的所有字段
        JSONObject condition = InformationSourceRequestBuilder.condition()
                .k3Id("test123")
                .mediaTag("tag123")
                .k3IdName("测试信息源名称")
                .organizer("测试主办单位")
                .name("测试账号名")
                .uid("test_uid_123")
                .mediaInfoTag(1)
                .mediaLevel("省级")
                .weiboVerifyType("金V")
                .verifyType("企业")
                .industry("科技行业")
                .followersCountRange(1000, 50000)
                .bigVLabel(1)
                .ipLocation("北京")
                .area("北京市")
                .status(0)
                .build();

        assertEquals("test123", condition.getString("k3Id"));
        assertEquals("tag123", condition.getString("mediaTag"));
        assertEquals("测试信息源名称", condition.getString("k3IdName"));
        assertEquals("测试主办单位", condition.getString("organizer"));
        assertEquals("测试账号名", condition.getString("name"));
        assertEquals("test_uid_123", condition.getString("uid"));
        assertEquals(1, condition.getIntValue("mediaInfoTag"));
        assertEquals("省级", condition.getString("mediaLevel"));
        assertEquals("金V", condition.getString("weiboVerifyType"));
        assertEquals("企业", condition.getString("verifyType"));
        assertEquals("科技行业", condition.getString("industry"));
        assertEquals(1000, condition.getIntValue("followersCountRangeFrom"));
        assertEquals(50000, condition.getIntValue("followersCountRangeTo"));
        assertEquals(1, condition.getIntValue("bigVLabel"));
        assertEquals("北京", condition.getString("ipLocation"));
        assertEquals("北京市", condition.getString("area"));
        assertEquals(0, condition.getIntValue("status"));
    }

    @Test
    void testPaginatorBuilderWithMultipleSorts() {
        // 测试分页构建器的多个排序条件
        JSONObject paginator = InformationSourceRequestBuilder.paginator(20, 50)
                .sorts("created_at", "desc")
                .sorts("followers_count", "asc")
                .build();

        assertEquals(20, paginator.getIntValue("from"));
        assertEquals(50, paginator.getIntValue("size"));
        
        assertTrue(paginator.containsKey("sorts"));
        assertEquals(2, paginator.getJSONArray("sorts").size());
        
        JSONObject firstSort = paginator.getJSONArray("sorts").getJSONObject(0);
        assertEquals("created_at", firstSort.getString("field"));
        assertEquals("desc", firstSort.getString("order"));
        
        JSONObject secondSort = paginator.getJSONArray("sorts").getJSONObject(1);
        assertEquals("followers_count", secondSort.getString("field"));
        assertEquals("asc", secondSort.getString("order"));
    }
}
