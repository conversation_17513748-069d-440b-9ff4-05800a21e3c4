package com.izhonghong.ubc.information.integration;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.controller.WeMediaController;
import com.izhonghong.ubc.information.elasticsearch.ElasticSearchAPI;
import com.izhonghong.ubc.information.entity.dto.WeMediaDTO;
import com.izhonghong.ubc.information.result.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * WeMediaController 集成测试
 * 测试从Controller到ElasticSearchAPI的完整调用链
 */
@ExtendWith(MockitoExtension.class)
public class WeMediaControllerIntegrationTest {

    @Mock
    private ElasticSearchAPI elasticSearchApi;

    @InjectMocks
    private WeMediaController weMediaController;

    @Test
    void testSelectPageWithNewPostRequest() {
        // 准备测试数据
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setK3Id(12345);
        weMediaDTO.setK3IdName("测试信息源");
        weMediaDTO.setOrganizer("测试主办单位");
        weMediaDTO.setName("测试账号名");
        weMediaDTO.setUid("test_uid_123");
        weMediaDTO.setMediaInfoTag(0);
        weMediaDTO.setAccountLevel("央级");
        weMediaDTO.setWeiboVerifyType("金V");
        weMediaDTO.setVerifyType("政务");
        weMediaDTO.setIndustry("科技行业");
        weMediaDTO.setFollowersCountRangeFrom("1000");
        weMediaDTO.setFollowersCountRangeTo("50000");
        weMediaDTO.setBigVLabel("1");
        weMediaDTO.setIpLocation(110000);
        weMediaDTO.setArea("北京市");
        weMediaDTO.setStatus(0);
        weMediaDTO.setPageNo(1);
        weMediaDTO.setSize(20);
        weMediaDTO.setSortField("created_at");
        weMediaDTO.setSort("desc");

        // 模拟ElasticSearchAPI返回结果
        JSONObject mockResult = new JSONObject();
        JSONObject data = new JSONObject();
        data.put("total", 100);
        data.put("records", "mock_records");
        mockResult.put("data", data);

        when(elasticSearchApi.selectInformation(any(WeMediaDTO.class))).thenReturn(mockResult);

        // 调用Controller方法
        Result result = weMediaController.selectPage(weMediaDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isFlag());
        assertNotNull(result.getData());
        
        JSONObject resultData = (JSONObject) result.getData();
        assertTrue(resultData.containsKey("data"));
    }

    @Test
    void testSelectPageWithMinimalData() {
        // 测试最小数据集
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setName("最小测试");

        JSONObject mockResult = new JSONObject();
        mockResult.put("data", new JSONObject());

        when(elasticSearchApi.selectInformation(any(WeMediaDTO.class))).thenReturn(mockResult);

        Result result = weMediaController.selectPage(weMediaDTO);

        assertNotNull(result);
        assertTrue(result.isFlag());
    }

    @Test
    void testSelectPageWithPaginationData() {
        // 测试分页数据
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setName("分页测试");
        weMediaDTO.setPageNo(3);
        weMediaDTO.setSize(50);
        weMediaDTO.setSortField("update_time");
        weMediaDTO.setSort("asc");

        JSONObject mockResult = new JSONObject();
        JSONObject data = new JSONObject();
        data.put("total", 500);
        data.put("current_page", 3);
        data.put("page_size", 50);
        mockResult.put("data", data);

        when(elasticSearchApi.selectInformation(any(WeMediaDTO.class))).thenReturn(mockResult);

        Result result = weMediaController.selectPage(weMediaDTO);

        assertNotNull(result);
        assertTrue(result.isFlag());
        
        JSONObject resultData = (JSONObject) result.getData();
        JSONObject dataObj = resultData.getJSONObject("data");
        assertEquals(500, dataObj.getIntValue("total"));
        assertEquals(3, dataObj.getIntValue("current_page"));
        assertEquals(50, dataObj.getIntValue("page_size"));
    }

    @Test
    void testSelectPageWithComplexSearchConditions() {
        // 测试复杂搜索条件
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setSourceType("微博");
        weMediaDTO.setName("复杂搜索测试");
        weMediaDTO.setArea("广东省深圳市");
        weMediaDTO.setOrganizer("深圳市政府");
        weMediaDTO.setAccountLevel("市级");
        weMediaDTO.setVerifyType("政务");
        weMediaDTO.setWeiboVerifyType("橙V");
        weMediaDTO.setIndustry("政务服务");
        weMediaDTO.setFollowersCountRangeFrom("10000");
        weMediaDTO.setFollowersCountRangeTo("100000");
        weMediaDTO.setBigVLabel("1");
        weMediaDTO.setStatus(1);
        weMediaDTO.setAccountType("政府机构");
        weMediaDTO.setVerifiedInfo("政府认证");
        weMediaDTO.setCode("GOV001");
        weMediaDTO.setBusinessCode("BUSINESS001");
        weMediaDTO.setRefreshType(0);

        JSONObject mockResult = new JSONObject();
        JSONObject data = new JSONObject();
        data.put("total", 25);
        data.put("filtered_results", "complex_search_results");
        mockResult.put("data", data);

        when(elasticSearchApi.selectInformation(any(WeMediaDTO.class))).thenReturn(mockResult);

        Result result = weMediaController.selectPage(weMediaDTO);

        assertNotNull(result);
        assertTrue(result.isFlag());
        
        JSONObject resultData = (JSONObject) result.getData();
        JSONObject dataObj = resultData.getJSONObject("data");
        assertEquals(25, dataObj.getIntValue("total"));
        assertEquals("complex_search_results", dataObj.getString("filtered_results"));
    }

    @Test
    void testSelectPageWithEmptyResult() {
        // 测试空结果
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setName("无结果测试");

        JSONObject mockResult = new JSONObject();
        JSONObject data = new JSONObject();
        data.put("total", 0);
        data.put("records", new JSONObject[0]);
        mockResult.put("data", data);

        when(elasticSearchApi.selectInformation(any(WeMediaDTO.class))).thenReturn(mockResult);

        Result result = weMediaController.selectPage(weMediaDTO);

        assertNotNull(result);
        assertTrue(result.isFlag());
        
        JSONObject resultData = (JSONObject) result.getData();
        JSONObject dataObj = resultData.getJSONObject("data");
        assertEquals(0, dataObj.getIntValue("total"));
    }

    @Test
    void testSelectPageWithNullResult() {
        // 测试null结果处理
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setName("null结果测试");

        when(elasticSearchApi.selectInformation(any(WeMediaDTO.class))).thenReturn(null);

        Result result = weMediaController.selectPage(weMediaDTO);

        assertNotNull(result);
        assertTrue(result.isFlag());
        assertNull(result.getData());
    }
}

/**
 * 用于演示新的JSON请求体结构的示例类
 */
class JsonRequestBodyExample {
    
    public static void main(String[] args) {
        // 演示如何构建符合要求的JSON请求体
        JSONObject condition = new JSONObject();
        condition.put("k3Id", "12345");
        condition.put("mediaTag", "test_tag");
        condition.put("k3IdName", "测试信息源名字");
        condition.put("organizer", "测试主办单位");
        condition.put("name", "账号名测试");
        condition.put("uid", "test_uid_123");
        condition.put("mediaInfoTag", 0);
        condition.put("mediaLevel", "央级");
        condition.put("weiboVerifyType", "金V");
        condition.put("verifyType", "政务");
        condition.put("industry", "科技行业");
        condition.put("followersCountRangeFrom", 0);
        condition.put("followersCountRangeTo", 1000);
        condition.put("bigVLabel", 1);
        condition.put("ipLocation", "北京");
        condition.put("area", "北京市");
        condition.put("status", 0);

        JSONObject paginator = new JSONObject();
        paginator.put("from", 0);
        paginator.put("size", 100);
        
        JSONObject sort = new JSONObject();
        sort.put("field", "created_at");
        sort.put("order", "desc");
        
        JSONObject[] sorts = {sort};
        paginator.put("sorts", sorts);

        JSONObject requestBody = new JSONObject();
        requestBody.put("condition", condition);
        requestBody.put("paginator", paginator);

        System.out.println("完整的JSON请求体结构:");
        System.out.println(requestBody.toJSONString());
        
        System.out.println("\n格式化的JSON请求体:");
        System.out.println(JSONObject.toJSONString(requestBody, true));
    }
}
