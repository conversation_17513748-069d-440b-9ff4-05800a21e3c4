package com.izhonghong.ubc.information.elasticsearch;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.config.WebCrawlerConfig;
import com.izhonghong.ubc.information.entity.dto.WeMediaDTO;
import com.izhonghong.ubc.information.service.MediaService;
import com.izhonghong.ubc.information.service.OperationLogService;
import com.izhonghong.ubc.information.util.SystemAlertUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * ElasticSearchAPI 测试类
 * 测试从WeMediaController.selectPage开始的完整调用链
 */
@ExtendWith(MockitoExtension.class)
public class ElasticSearchAPITest {

    @Mock
    private WebCrawlerConfig webCrawlerConfig;

    @Mock
    private MediaService mediaService;

    @Mock
    private OperationLogService operationLogService;

    @Mock
    private SystemAlertUtils systemAlertUtils;

    @InjectMocks
    private ElasticSearchAPI elasticSearchAPI;

    @BeforeEach
    void setUp() {
        when(webCrawlerConfig.getServerUrl()).thenReturn("http://test-server");
    }

    @Test
    void testBuildSearchRequestBodyWithBasicFields() {
        // 创建测试用的WeMediaDTO
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setK3Id(12345);
        weMediaDTO.setMediaTag("test_tag");
        weMediaDTO.setK3IdName("测试信息源名称");
        weMediaDTO.setOrganizer("测试主办单位");
        weMediaDTO.setName("测试账号名");
        weMediaDTO.setUid("test_uid_123");
        weMediaDTO.setAccountLevel("央级");
        weMediaDTO.setWeiboVerifyType("金V");
        weMediaDTO.setVerifyType("政务");
        weMediaDTO.setIndustry("科技行业");
        weMediaDTO.setArea("北京市");
        weMediaDTO.setStatus(0);
        weMediaDTO.setPageNo(1);
        weMediaDTO.setSize(20);
        weMediaDTO.setSortField("created_at");
        weMediaDTO.setSort("desc");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = ElasticSearchAPI.class.getDeclaredMethod("buildSearchRequestBody", WeMediaDTO.class);
            method.setAccessible(true);
            JSONObject requestBody = (JSONObject) method.invoke(elasticSearchAPI, weMediaDTO);

            // 验证请求体结构
            assertNotNull(requestBody);
            assertTrue(requestBody.containsKey("condition"));
            assertTrue(requestBody.containsKey("paginator"));

            // 验证condition字段
            JSONObject condition = requestBody.getJSONObject("condition");
            assertNotNull(condition);
            assertEquals("12345", condition.getString("k3Id"));
            assertEquals("test_tag", condition.getString("mediaTag"));
            assertEquals("测试信息源名称", condition.getString("k3IdName"));
            assertEquals("测试主办单位", condition.getString("organizer"));
            assertEquals("测试账号名", condition.getString("name"));
            assertEquals("test_uid_123", condition.getString("uid"));
            assertEquals("央级", condition.getString("mediaLevel"));
            assertEquals("金V", condition.getString("weiboVerifyType"));
            assertEquals("政务", condition.getString("verifyType"));
            assertEquals("科技行业", condition.getString("industry"));
            assertEquals("北京市", condition.getString("area"));
            assertEquals(0, condition.getIntValue("status"));

            // 验证paginator字段
            JSONObject paginator = requestBody.getJSONObject("paginator");
            assertNotNull(paginator);
            assertEquals(0, paginator.getIntValue("from")); // (pageNo-1) * size = (1-1) * 20 = 0
            assertEquals(20, paginator.getIntValue("size"));

            // 验证排序
            assertTrue(paginator.containsKey("sorts"));
            assertEquals(1, paginator.getJSONArray("sorts").size());
            JSONObject sort = paginator.getJSONArray("sorts").getJSONObject(0);
            assertEquals("created_at", sort.getString("field"));
            assertEquals("desc", sort.getString("order"));

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildSearchRequestBodyWithFollowersRange() {
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setFollowersCountRangeFrom("1000");
        weMediaDTO.setFollowersCountRangeTo("50000");
        weMediaDTO.setBigVLabel("1");
        weMediaDTO.setPageNo(2);
        weMediaDTO.setSize(50);

        try {
            java.lang.reflect.Method method = ElasticSearchAPI.class.getDeclaredMethod("buildSearchRequestBody", WeMediaDTO.class);
            method.setAccessible(true);
            JSONObject requestBody = (JSONObject) method.invoke(elasticSearchAPI, weMediaDTO);

            JSONObject condition = requestBody.getJSONObject("condition");
            assertEquals(1000, condition.getIntValue("followersCountRangeFrom"));
            assertEquals(50000, condition.getIntValue("followersCountRangeTo"));
            assertEquals(1, condition.getIntValue("bigVLabel"));

            JSONObject paginator = requestBody.getJSONObject("paginator");
            assertEquals(50, paginator.getIntValue("from")); // (2-1) * 50 = 50
            assertEquals(50, paginator.getIntValue("size"));

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildSearchRequestBodyWithDefaultValues() {
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        // 不设置任何值，测试默认值

        try {
            java.lang.reflect.Method method = ElasticSearchAPI.class.getDeclaredMethod("buildSearchRequestBody", WeMediaDTO.class);
            method.setAccessible(true);
            JSONObject requestBody = (JSONObject) method.invoke(elasticSearchAPI, weMediaDTO);

            JSONObject paginator = requestBody.getJSONObject("paginator");
            assertEquals(0, paginator.getIntValue("from")); // 默认页码0
            assertEquals(20, paginator.getIntValue("size")); // 默认大小20

            // 验证默认排序
            JSONObject sort = paginator.getJSONArray("sorts").getJSONObject(0);
            assertEquals("created_at", sort.getString("field")); // 默认排序字段
            assertEquals("desc", sort.getString("order")); // 默认排序方向

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildSearchRequestBodyWithInvalidNumbers() {
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setFollowersCountRangeFrom("invalid_number");
        weMediaDTO.setFollowersCountRangeTo("also_invalid");
        weMediaDTO.setBigVLabel("not_a_number");

        try {
            java.lang.reflect.Method method = ElasticSearchAPI.class.getDeclaredMethod("buildSearchRequestBody", WeMediaDTO.class);
            method.setAccessible(true);
            JSONObject requestBody = (JSONObject) method.invoke(elasticSearchAPI, weMediaDTO);

            JSONObject condition = requestBody.getJSONObject("condition");
            // 无效数字应该被忽略，不应该出现在condition中
            assertFalse(condition.containsKey("followersCountRangeFrom"));
            assertFalse(condition.containsKey("followersCountRangeTo"));
            assertFalse(condition.containsKey("bigVLabel"));

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildSearchRequestBodyWithAllMediaFields() {
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setMedia("媒体性质");
        weMediaDTO.setMedia_type("微博");
        weMediaDTO.setMediaLevel("省级");
        weMediaDTO.setIndustry("教育行业");
        weMediaDTO.setWeb_url("http://test.com");
        weMediaDTO.setHomeUrl("http://home.test.com");
        weMediaDTO.setAccountType("企业");
        weMediaDTO.setVerifiedInfo("已认证");
        weMediaDTO.setCode("TEST001");
        weMediaDTO.setBusinessCode("BIZ001");
        weMediaDTO.setRefreshType(1);

        try {
            java.lang.reflect.Method method = ElasticSearchAPI.class.getDeclaredMethod("buildSearchRequestBody", WeMediaDTO.class);
            method.setAccessible(true);
            JSONObject requestBody = (JSONObject) method.invoke(elasticSearchAPI, weMediaDTO);

            JSONObject condition = requestBody.getJSONObject("condition");
            assertEquals("媒体性质", condition.getString("media"));
            assertEquals("微博", condition.getString("media_type"));
            assertEquals("省级", condition.getString("mediaLevel"));
            assertEquals("教育行业", condition.getString("industry"));
            assertEquals("http://test.com", condition.getString("web_url"));
            assertEquals("http://home.test.com", condition.getString("homeUrl"));
            assertEquals("企业", condition.getString("accountType"));
            assertEquals("已认证", condition.getString("verifiedInfo"));
            assertEquals("TEST001", condition.getString("code"));
            assertEquals("BIZ001", condition.getString("businessCode"));
            assertEquals(1, condition.getIntValue("refreshType"));

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildSearchRequestBodyJsonStructure() {
        WeMediaDTO weMediaDTO = new WeMediaDTO();
        weMediaDTO.setK3Id(123);
        weMediaDTO.setName("测试");
        weMediaDTO.setPageNo(1);
        weMediaDTO.setSize(10);

        try {
            java.lang.reflect.Method method = ElasticSearchAPI.class.getDeclaredMethod("buildSearchRequestBody", WeMediaDTO.class);
            method.setAccessible(true);
            JSONObject requestBody = (JSONObject) method.invoke(elasticSearchAPI, weMediaDTO);

            // 验证JSON结构符合要求的格式
            String jsonString = requestBody.toJSONString();
            assertTrue(jsonString.contains("\"condition\""));
            assertTrue(jsonString.contains("\"paginator\""));
            assertTrue(jsonString.contains("\"from\""));
            assertTrue(jsonString.contains("\"size\""));
            assertTrue(jsonString.contains("\"sorts\""));

            // 打印JSON结构用于调试
            System.out.println("生成的JSON请求体:");
            System.out.println(requestBody.toJSONString());

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }
}
