package com.izhonghong.ubc.information.result;

import com.google.common.collect.ImmutableMap;

/**
 * 状态码 枚举类
 * 20000 成功
 * 3**** 预留
 * 4**** 系统相关状态码
 * 5**** 业务相关状态码
 */
public enum StatusCode {

    SUCCESS("200", "success"),
    SYS_DATA_INVALID_PARAM("401001","参数不合法"),
    SYS_DATA_NOT_EXISTS("401002", "数据不存在"),
    SYS_DATA_ADD_FAIL("401003", "数据添加失败"),
    SYS_DATA_UPDATE_FAIL("401004", "数据修改失败"),
    SYS_DATA_DELETE_FAIL("401005", "数据删除失败"),
    SYS_AUTH_ACCOUNT_EXISTS("402001", "用户已注册"),
    SYS_AUTH_ACCOUNT_NOT_EXISTS("402002", "用户不存在"),
    SYS_AUTH_ROLE_NOT_EXISTS("402003", "角色不存在"),
    SYS_AUTH_PERMISSION_NOT_NULL("402004", "权限列表不能为空"),
    SYS_AUTH_INVALID_ENABLE("402005", "用户已禁用"),
    SYS_AUTH_NEW_CONFIRM_DISCREPANCY("402006", "新密码与确认密码不一致"),
    SYS_AUTH_ACCOUNT_NOT_OAUTH("402007", "未登录"),
    SYS_AUTH_ACCOUNT_NO_PERMISSION("402008", "无权限访问"),
    SYS_AUTH_ACCOUNT_LOCKED("402009", "用户被锁定"),
    SYS_AUTH_ACCOUNT_PWD_ERR("402010", "用户或密码错误"),
    SYS_AUTH_INVALID_SIG("402011", "无效的签名"),
    SYS_AUTH_INVALID_TOKEN("402012", "access-token 已失效"),
    SYS_COMMON_API_NOT_EXIST("403001", "请求接口不存在"),
    SYS_COMMON_INVALID_REQUEST_METHOD("403002", "无效请求方法"),
    SYS_COMMON_CONNECTION_TIMEOUT("403003", "连接超时"),
    SYS_COMMON_RATE_LIMIT_REQUEST("403004", "操作过快, 请勿频繁操作"),
    SYS_COMMON_DUPLICATE_REQUEST("403005", "正在处理中, 请勿重复操作或稍后再试"),
    SYS_COMMON_IO_READ_ERROR("403006", "IO读异常"),
    SYS_COMMON_INVALID_PARAM("403007", "无效的请求参数"),
    SYS_COMMON_INVALID_HEARTHIT("403008", "无效的心跳请求"),
    SYS_COMMON_SERVER_UNKNOWN_ERROR("403009", "服务端异常, 请稍后再试"),
    SYS_COMMON_SERVICE_BUSY("403010", "系统繁忙"),
    SYS_COMMON_DATA_CONFIG_FAIL("403011", "配置错误"),
    SYS_COMMON_OPERATION_FAIL("403012", "操作失败"),
    SYS_COMMON_INVALID_VERIFICATION_CODE("403013","验证码已失效"),
    SYS_COMMON_VERIFICATION_CODE_ERR("403014","验证码错误"),
    SYS_COMMON_REQFREQUENCY_ERR("403015","请勿频繁操作"),
    SYS_COMMON_EXCEL_IMPORT_ERR("403016","EXCEL 导入失败"),
    // 导入数据失败,未找到匹配数据
    SYS_COMMON_EXCEL_DATA_ERR("403017","EXCEL 导入失败，未找到匹配数据"),
    // 导入数据失败,超过最大行数
    SYS_COMMON_EXCEL_IMPORT_EXCEEDS_MAX_NUM_ERR("403018","EXCEL 导入失败，超过最大导入行数"),
    // 导入失败，请检查文件是否上传或填写正确
    SYS_COMMON_EXCEL_FILE_ERR("403019","导入失败，请检查文件是否上传或填写正确"),
    
    SYS_COMMON_EXCEL_EXPORT_ERR("403020","EXCEL 导出失败"),
    
    SYS_COMMON_EXCEL_EXPORT_DATA_ERR("403021","EXCEL 导出失败，未找到匹配数据"),

    K3_AUTH_ERROR("501001","K3登录失败"),
    AMS_AUTH_ERROR("501002","AMS登录失败"),
    KYS_AUTH_ERROR("501003","网络延迟，请稍后"),
    ;

    private String code;
    private String message;
    private static final ImmutableMap<String, StatusCode> CACHE;

    StatusCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    static {
        final ImmutableMap.Builder<String, StatusCode> builder = ImmutableMap.builder();
        for (StatusCode statusCode : values()) {
            builder.put(statusCode.code(), statusCode);
        }
        CACHE = builder.build();
    }

    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    /**
     * 根据 code 获取对应枚举
     * @param code
     * @return
     */
    public static StatusCode valueOfCode(String code) {
        final StatusCode status = CACHE.get(code);
        if (status == null) {
            throw new IllegalArgumentException("No matching constant for [" + code + "]");
        }
        return status;
    }

}
