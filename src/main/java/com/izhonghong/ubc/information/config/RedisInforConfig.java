package com.izhonghong.ubc.information.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@Data
@Component
@ConfigurationProperties("redis.server")
public class RedisInforConfig {
	
	    private String hostName;
	    
	    private String password;

	    private Integer port;

	    private Integer database;
	        
	    private Integer timeout;
		
	    private Integer maxIdle;

	    private Integer maxTotal;

	    private Integer maxWaitMillis;

	    private Integer minEvictableIdleTimeMillis;

	    private Integer numTestsPerEvictionRun;

	    private long timeBetweenEvictionRunsMillis;

	    private Boolean testOnBorrow;

	    private Boolean testWhileIdle;

}
