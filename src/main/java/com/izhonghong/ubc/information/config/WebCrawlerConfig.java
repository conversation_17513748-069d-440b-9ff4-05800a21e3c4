package com.izhonghong.ubc.information.config;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@Data
@Component
@ConfigurationProperties("webcrawler.server")
public class WebCrawlerConfig {
	/**
	 * 采集服务器的连接
	 * ***/
	private String serverUrl;

	private String spiderApiServer;

	/**
	 * 平台类型的id与名称映射
	 * ***/
	private Map<Integer,String> mediaMaps;

	/**
	 * 服务器的临时文件存储地址
	 * ***/
	private String fileBasedir;

	/**
	 * 推送的任务间隔配置
	 * **/
	private String pushTimer;

	private String traditionalSiteServer;
}
