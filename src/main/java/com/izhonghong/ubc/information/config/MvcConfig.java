package com.izhonghong.ubc.information.config;

import com.izhonghong.ubc.information.resolver.PreventInjectArgumentResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023-07-02 20:08
 * @menu
 */
@Configuration
@Slf4j
public class MvcConfig {

    @Autowired
    private RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @Autowired
    private List<HttpMessageConverter<?>> messageConverters;


    @PostConstruct
    public void initArgumentResolvers(){
        List<HandlerMethodArgumentResolver> argumentResolvers = requestMappingHandlerAdapter.getArgumentResolvers();
        List<HandlerMethodArgumentResolver> newResolvers = new ArrayList<>();
        newResolvers.add(new PreventInjectArgumentResolver(messageConverters));
        newResolvers.addAll(argumentResolvers);
        requestMappingHandlerAdapter.setArgumentResolvers(newResolvers);
    }

}
