package com.izhonghong.ubc.information.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="ubc.cas")
public class CasConfig {
	
	/**
	 * 是否开启使用cas配置
	 * */
	private boolean openCas;
	
	/**
	 * cas的地址
	 * **/
	private String ubcCasServe;



	public boolean getOpenCas() {
		return openCas;
	}

	public void setOpenCas(boolean openCas) {
		this.openCas = openCas;
	}

	public String getUbcCasServe() {
		return ubcCasServe;
	}

	public void setUbcCasServe(String ubcCasServe) {
		this.ubcCasServe = ubcCasServe;
	}
	
	
	

}
