package com.izhonghong.ubc.information.elasticsearch;

import cn.cnhon.util.MD5;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izhonghong.ubc.information.common.enums.*;
import com.izhonghong.ubc.information.config.WebCrawlerConfig;
import com.izhonghong.ubc.information.constant.ArticleTypeEnum;
import com.izhonghong.ubc.information.constant.MediaCodeEnum;
import com.izhonghong.ubc.information.constant.SystemModuleEnum;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.entity.MediaLibraryInformationSourceBean;
import com.izhonghong.ubc.information.entity.OperationLog;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.dto.InformationSourceSearchRequestDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDeleteDTO;
import com.izhonghong.ubc.information.entity.vo.AccountInfoVo;
import com.izhonghong.ubc.information.service.MediaService;
import com.izhonghong.ubc.information.service.OperationLogService;
import com.izhonghong.ubc.information.util.StringUtil;
import com.izhonghong.ubc.information.util.bean.ReflectionUtils;
import com.izhonghong.ubc.information.util.components.ISearchDataAccess;
import com.izhonghong.ubc.information.util.InformationSourceRequestBuilder;
import com.izhonghong.ubc.information.util.components.SystemAlertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 对接数据组的es
 * <AUTHOR>
 *
 * ***/
@Slf4j
@Component
public class ElasticSearchAPI {

	@Autowired
	private WebCrawlerConfig  webCrawlerConfig;

	@Autowired
	private SystemAlertUtils systemAlertUtils;

	@Autowired
	private MediaService mediaService;

	@Autowired
    private OperationLogService operationLogService;

	@Value("${webcrawler.server.fullLibAccountPageUrl}")
	private String FullLibAccountPageUrl;

	/***
	 * 数据 的综合查询
	 *
	 * ***/
	public JSONObject search(MediaBaseDTO  mediaBase) {
		MediaLibraryDTO mediaLibraryDTO = (MediaLibraryDTO) mediaBase;
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		if(!StringUtils.isEmpty(mediaBase.getArea())) {
			MediaLibraryInformationSourceBean library = new MediaLibraryInformationSourceBean();
			mediaLibraryDTO.setArea(library.cleanArea(mediaBase.getArea()));
		}
		if (CollUtil.isNotEmpty(mediaBase.getCodeList())) {
			informationSourceBean.setCodeList(mediaBase.getCodeList());
		}
		ReflectionUtils.copyProperties(informationSourceBean, mediaLibraryDTO);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), WebSpiderNewDataUrlEnum.ES_SEARCH.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).search(informationSourceBean);
			 setResult(result);
		} catch (Exception e) {
			log.error("ElasticSearchAPI search error.",e);
			systemAlert("数据综合查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	}

	/**
	 * 数据的综合查询 - 支持JSON请求体
	 * @param searchRequest JSON请求体
	 * @return 查询结果
	 */
	public JSONObject searchWithJson(InformationSourceSearchRequestDTO searchRequest) {
		try {
			// 创建信息源配置
			InformationSourceBean informationSourceBean = new InformationSourceBean();
			informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), WebSpiderNewDataUrlEnum.ES_SEARCH.getUrl());

			// 构建JSON请求体
			JSONObject requestBody = buildJsonRequestBody(searchRequest);

			// 调用新的搜索方法
			JSONObject result = WebSpiderInformationSource.INSTANCE
					.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
					.searchInformationSource(informationSourceBean, requestBody);

			// 处理结果
			setResult(result);
			return result;

		} catch (Exception e) {
			log.error("ElasticSearchAPI searchWithJson error.", e);
			systemAlert("JSON请求体搜索异常  \n" + e);
			e.printStackTrace();
			return new JSONObject();
		}
	}

	/**
	 * 构建JSON请求体
	 * @param searchRequest 搜索请求DTO
	 * @return JSON请求体
	 */
	private JSONObject buildJsonRequestBody(InformationSourceSearchRequestDTO searchRequest) {
		InformationSourceSearchRequestDTO.ConditionDTO condition = searchRequest.getCondition();
		InformationSourceSearchRequestDTO.PaginatorDTO paginator = searchRequest.getPaginator();

		// 构建condition对象
		InformationSourceRequestBuilder.ConditionBuilder conditionBuilder = InformationSourceRequestBuilder.condition();

		if (condition != null) {
			if (!StringUtils.isEmpty(condition.getK3Id())) {
				conditionBuilder.k3Id(condition.getK3Id());
			}
			if (!StringUtils.isEmpty(condition.getMediaTag())) {
				conditionBuilder.mediaTag(condition.getMediaTag());
			}
			if (!StringUtils.isEmpty(condition.getK3IdName())) {
				conditionBuilder.k3IdName(condition.getK3IdName());
			}
			if (!StringUtils.isEmpty(condition.getOrganizer())) {
				conditionBuilder.organizer(condition.getOrganizer());
			}
			if (!StringUtils.isEmpty(condition.getName())) {
				conditionBuilder.name(condition.getName());
			}
			if (!StringUtils.isEmpty(condition.getUid())) {
				conditionBuilder.uid(condition.getUid());
			}
			if (condition.getMediaInfoTag() != null) {
				conditionBuilder.mediaInfoTag(condition.getMediaInfoTag());
			}
			if (!StringUtils.isEmpty(condition.getMediaLevel())) {
				conditionBuilder.mediaLevel(condition.getMediaLevel());
			}
			if (!StringUtils.isEmpty(condition.getWeiboVerifyType())) {
				conditionBuilder.weiboVerifyType(condition.getWeiboVerifyType());
			}
			if (!StringUtils.isEmpty(condition.getVerifyType())) {
				conditionBuilder.verifyType(condition.getVerifyType());
			}
			if (!StringUtils.isEmpty(condition.getIndustry())) {
				conditionBuilder.industry(condition.getIndustry());
			}
			if (condition.getFollowersCountRangeFrom() != null || condition.getFollowersCountRangeTo() != null) {
				conditionBuilder.followersCountRange(condition.getFollowersCountRangeFrom(), condition.getFollowersCountRangeTo());
			}
			if (condition.getBigVLabel() != null) {
				conditionBuilder.bigVLabel(condition.getBigVLabel());
			}
			if (!StringUtils.isEmpty(condition.getIpLocation())) {
				conditionBuilder.ipLocation(condition.getIpLocation());
			}
			if (!StringUtils.isEmpty(condition.getArea())) {
				conditionBuilder.area(condition.getArea());
			}
			if (condition.getStatus() != null) {
				conditionBuilder.status(condition.getStatus());
			}
		}

		// 构建paginator对象
		InformationSourceRequestBuilder.PaginatorBuilder paginatorBuilder =
				InformationSourceRequestBuilder.paginator(paginator.getFrom(), paginator.getSize());

		// 添加排序条件
		if (paginator.getSorts() != null && !paginator.getSorts().isEmpty()) {
			for (InformationSourceSearchRequestDTO.SortDTO sort : paginator.getSorts()) {
				paginatorBuilder.sorts(sort.getField(), sort.getOrder());
			}
		}

		// 构建完整请求体
		return InformationSourceRequestBuilder.buildSearchRequest(
				conditionBuilder.build(),
				paginatorBuilder.build()
		);
	}

	/**
	 * 新增或修改
	 * **/
	public  JSONObject saveOrUpdate(MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean) {
		String url = WebSpiderNewDataUrlEnum.ES_INSERT.getUrl();
		if(mediaLibraryInformationSourceBean.getRequestType() != null && mediaLibraryInformationSourceBean.getRequestType().equals(1)) {
			url = WebSpiderNewDataUrlEnum.ES_UPDATE.getUrl();
		}
		mediaLibraryInformationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), url);
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).saveOrUpdate(mediaLibraryInformationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI saveOrUpdate error.",e);
			systemAlert("新增或修改异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	};

	/**
	 *删除
	 * **/
	public  JSONObject delete(MediaLibraryInformationSourceBean  mediaLibraryInformationSourceBean) {
		mediaLibraryInformationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_DELETE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).delete(mediaLibraryInformationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI delete error.",e);
			systemAlert("数据删除异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	}


	/**
	 *
	 * 根据id查询
	 * ***/
	public  JSONObject searchById(MediaBaseDTO  mediaBase) {
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_SEARCH_MID.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).search(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI searchById error.",e);
			systemAlert("根据id查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;

	}

	public JSONObject getSiteInfoById(Integer siteId) {
		String apiUrl = String.format("%s/traditional/informationByid?k3_id=%s", webCrawlerConfig.getTraditionalSiteServer(), siteId);
		JSONObject resultJson = new JSONObject();
		try {
			resultJson = ISearchDataAccess.getAndHasData(apiUrl);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resultJson;
	}

	public JSONObject selectMediaTypeTree(MediaBaseDTO mediaBase) {
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_MEDIA_TYPE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).searchMediaType(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI selectMediaTypeTree error.",e);
			systemAlert("获取所有媒体类型树查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	public JSONObject selectInformation(MediaBaseDTO mediaBase) {
		WeMediaDTO weMediaDTO = (WeMediaDTO) mediaBase;
		InformationSourceBean informationSourceBean = setInformationSourceBean(weMediaDTO);
		Integer page =  weMediaDTO.getPageNo() == null ? 0 : weMediaDTO.getPageNo() - 1;
		Integer size =  weMediaDTO.getSize() == null ? 20 :weMediaDTO.getSize();
		String source =  StringUtils.isEmpty(weMediaDTO.getSourceType()) ? "微信" : weMediaDTO.getSourceType();
		String sourceType = source;
		if(source.equals("今日头条")){
			sourceType = "头条";
		}
		String url = String.format(WebSpiderNewDataUrlEnum.ES_SEARCH_INFORMATION.getUrl(),sourceType,page*size,size);
		if(!StringUtils.isEmpty(weMediaDTO.getAccountType())) {
			url = url+"&accountType="+weMediaDTO.getAccountType();
		}
		if(!StringUtils.isEmpty(weMediaDTO.getArea())) {
//			url = url+"&area="+informationSourceBean.cleanArea(weMediaDTO.getArea());
			url = url+"&area="+weMediaDTO.getArea();
		}
		if(!StringUtils.isEmpty(weMediaDTO.getName())) {
			url = url+"&name="+weMediaDTO.getName();
		}

		if(!StringUtils.isEmpty(weMediaDTO.getStatus())) {
			url = url+"&status="+weMediaDTO.getStatus();
		}

		if(!StringUtils.isEmpty(weMediaDTO.getAccountLevel())) {
			url = url+"&accountLevel="+weMediaDTO.getAccountLevel();
		}
		if(!StringUtils.isEmpty(weMediaDTO.getVerifiedInfo())) {
			url = url+"&verifiedInfo="+weMediaDTO.getVerifiedInfo();
		}

		if(!StringUtils.isEmpty(weMediaDTO.getSortField())) {
			url = url+"&sortField="+weMediaDTO.getSortField();
		}
		if(!StringUtils.isEmpty(weMediaDTO.getSort())) {
			url = url+"&sortValue="+weMediaDTO.getSort();
		}
		if (!StringUtils.isEmpty(weMediaDTO.getOrganizer())){
			url = url+"&organizer="+weMediaDTO.getOrganizer();
		}
		if (!StringUtils.isEmpty(weMediaDTO.getCode())){
			url = url+"&code="+weMediaDTO.getCode();
		}

		if (!StringUtils.isEmpty(weMediaDTO.getBusinessCode())) {
			url = url + "&businessCode=" + weMediaDTO.getBusinessCode();
		}

		if (null != weMediaDTO.getRefreshType()) {
			url = url + "&refreshType=" + weMediaDTO.getRefreshType();
		}

		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),url);
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).searchInformationSource(informationSourceBean);
			 setResult(result, source);
		} catch (Exception e) {
			log.error("ElasticSearchAPI delete error.",e);
			systemAlert("根据id查询异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	private void setResult(JSONObject result,String source) {
		Map<String, String> mediaType = mediaService.mediaType();
		if(StringUtils.isEmpty(result)) {
			return;
		}
		JSONArray array = result.getJSONArray("data");
		if(StringUtils.isEmpty(array)) {
			return;
		}
		for (int i = 0; i < array.size(); i++) {
			JSONObject json = array.getJSONObject(i);
			json.put("sourceType", source);
			Object code = null == json.get("code") ? mediaType.get(json.getString("accountLevel")) : json.get("code");
			json.put("code", code);
			if(SourceTypeMenu.WE_CHAT.getValue().equals(source)) {
				String homeUrl = "https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz="+json.getString("uid");
				json.put("homeUrl", homeUrl);
			}

			OperationLog logs = operationLogService.selectOperationLogByUid(json.getString("uid"));
			if(logs != null) {
				json.put("operationLog", logs);
			}
			json.put("verifiedType", null);
			if (json.getString("code") != null && json.getString("code").startsWith("101") && json.getString("code").contains(":")) {
				String secondCode = json.getString("code").substring(0, 6);
				json.put("verifiedType", MediaCodeEnum.getNameByCode(secondCode));
			}
			json.put("mediaType", MediaCodeEnum.getNameByCode(json.getString("code")));
		}
	}

	private void setResult(JSONObject result) {
		Map<String,String> mediaType = mediaService.mediaTypeMap();
		if(StringUtils.isEmpty(result)) {
			return;
		}
		JSONArray array = result.getJSONArray("data");
		if(StringUtils.isEmpty(array)) {
			return;
		}
		for (int i = 0; i < array.size(); i++) {
			JSONObject json = array.getJSONObject(i);
			json.put("media_type", mediaType.get(json.getString("code")));
			if (StringUtil.isEmpty(json.getString("code")) || !json.getString("code").contains(":")) {
				json.put("second_level_type", mediaType.get(json.getString("code")));
			} else {
				json.put("second_level_type", mediaType.get(json.getString("code").substring(0, 6)));
			}
			json.put("type", ArticleTypeEnum.getArticleTypeValueByMediaCode(json.getString("code").substring(0, 3)));
			String uid = MD5.encode(json.getString("web_url"));
			json.put("uid", uid);
			OperationLog logs = operationLogService.selectOperationLogByUid(uid);
			if(logs != null) {
				json.put("operationLog", logs);
			}

		}

	}

	private InformationSourceBean setInformationSourceBean(MediaBaseDTO mediaBase) {
		InformationSourceBean informationSourceBean = new InformationSourceBean();
		ReflectionUtils.copyProperties(informationSourceBean, mediaBase);
		return informationSourceBean;
	}


	private void systemAlert(String message) {
		systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_ELASTIC_SEARCH, message);
	}

	public JSONObject deleteWeMedia(WeMediaDeleteDTO deleteDTO) {
		String apiUrl = String.format("%s%s", webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_WE_MEDIA_DELETE.getUrl());
		JSONObject result = new JSONObject();
		try {
			result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).deleteWeMedia(apiUrl, deleteDTO);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("ElasticSearchAPI deleteWeMedia error.",e);
			systemAlert("自媒体删除异常  \n"+e);
		}
		return result;
	}

	public JSONObject WeMediaSaveOrUpdate(WeMediaInformationSourceBean informationSourceBean) {

		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_WE_MEDIA_INSERT_OR_UPDATE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).weMediaSaveOrUpdate(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI saveOrUpdate error.",e);
			systemAlert("自媒体新增或修改异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	public JSONObject weMediaBatchSaveOrUpdate(WeMediaInformationSourceBean informationSourceBean) {
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(),WebSpiderNewDataUrlEnum.ES_WE_MEDIA_INSERT_OR_UPDATE.getUrl());
		JSONObject result = new JSONObject();
		try {
			 result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).weMediaSaveOrUpdate(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI saveOrUpdate error.",e);
			systemAlert("自媒体新增或修改异常  \n"+e);
			e.printStackTrace();
		}
		return result;
	}

	public JSONObject getWeMediaPlatformType(MediaBaseDTO mediaBase) {
		InformationSourceBean informationSourceBean = setInformationSourceBean(mediaBase);
		informationSourceBean.setInformationService(webCrawlerConfig.getServerUrl(), WebSpiderNewDataUrlEnum.ES_ALL_WE_MEDIA_PLATFORM_TYPE.getUrl());
		JSONObject result = new JSONObject();
		try {
			result = WebSpiderInformationSource.INSTANCE.getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode()).searchMediaType(informationSourceBean);
		} catch (Exception e) {
			log.error("ElasticSearchAPI getWeMediaPlatformType error.", e);
			systemAlert("获取全部的自媒体平台类型查询异常  \n" + e);
		}
		return result;
	}

	public Page<AccountInfoVo> selectAccountPage(WeMediaDTO weMediaDTO) {
		Page<AccountInfoVo> pageResult=new Page<>();
		if(StrUtil.isEmpty(weMediaDTO.getName())){
			return pageResult;
		}
		Map<String,String> mediaTypeMap=new HashMap<>();
		mediaTypeMap.put("微信","0");mediaTypeMap.put("抖音.APP","20382");mediaTypeMap.put("微博","1");
		mediaTypeMap.put("头条","10");mediaTypeMap.put("小红书","21583");mediaTypeMap.put("视频号","33230");
		weMediaDTO.setMedia_type(mediaTypeMap.get(weMediaDTO.getSourceType()));
		List<AccountInfoVo> records = searchFullLibAccount(weMediaDTO);
		for (AccountInfoVo item : records) {
			item.setType(Integer.parseInt(weMediaDTO.getMedia_type()));
		}
		pageResult.setRecords(records);
		pageResult.setTotal(records.size());
		return pageResult;
	}

	private List<AccountInfoVo> searchFullLibAccount(WeMediaDTO weMediaDTO) {
		Integer page =  weMediaDTO.getPageNo() == null ? 1 : weMediaDTO.getPageNo();
		String url= String.format(FullLibAccountPageUrl, weMediaDTO.getMedia_type(),page);
		String resStr=null;
		List<AccountInfoVo> result=new ArrayList<>();
		try{
			if(StrUtil.isNotEmpty(weMediaDTO.getName())){
				url+="&name="+weMediaDTO.getName();
			}
			HttpRequest post = HttpUtil.createPost(url);
			HttpResponse httpResponse = post.execute();
			resStr=httpResponse.body();
			JSONObject resObj = JSON.parseObject(resStr);
			JSONArray data = resObj.getJSONArray("data");
			for (int i = 0; i < data.size(); i++) {
				JSONObject jsonObject = data.getJSONObject(i);
				AccountInfoVo item=new AccountInfoVo();
				item.setUid(jsonObject.getString("uid"));
				item.setHomeUrl(jsonObject.getString("url"));
				item.setPlatName(weMediaDTO.getSourceType());
				item.setAccount(jsonObject.getString("accout"));
				item.setName(jsonObject.getString("name"));
				result.add(item);
			}
		}catch (Exception e){
			log.error("searchFullLibAccount 异常",e);
		}finally {
			log.info("url="+url);
			log.info("param="+JSON.toJSONString(weMediaDTO));
			log.info("resStr="+resStr);
		}
		return result;
	}

}
