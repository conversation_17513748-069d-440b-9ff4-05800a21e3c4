package com.izhonghong.ubc.information.resolver;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.dto.SearchDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDTO;

import com.izhonghong.ubc.information.util.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;

import java.util.List;

/**
 * 请求参数解析器
 */
@Slf4j
public class PreventInjectArgumentResolver extends RequestResponseBodyMethodProcessor {

	public PreventInjectArgumentResolver(List<HttpMessageConverter<?>> converters) {
		super(converters);
	}

    /**
     * 判断是否是支持解析的参数
     * @param parameter
     * @return
     */
	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		return parameter.hasParameterAnnotation(RequestBody.class)
				&& (parameter.getParameterType().equals(WeMediaDTO.class)
				|| parameter.getParameterType().equals(SearchDTO.class)
				|| parameter.getParameterType().equals(MediaLibraryDTO.class));
	}

	/**
     * 解析参数
     * @param parameter
     * @param mavContainer
     * @param webRequest
     * @param binderFactory
     * @return
     * @throws Exception
     */
	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) throws Exception {
		Object obj=this.readWithMessageConverters(webRequest, parameter, parameter.getParameterType());
		JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(obj));
		Assert.require(checkValue(jsonObject),"参数不合法!");
		return obj;
	}

	//参数校验
	public boolean checkValue(JSONObject jsonObject){
		if(jsonObject.containsKey("sort")){
			String sort=jsonObject.getString("sort");
			if(StringUtils.isNotEmpty(sort) && NumberUtil.isNumber(sort) && Integer.valueOf(sort)>1000){
				return false;
			}
		}
		return true;
	}
}
