package com.izhonghong.ubc.information.example;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.common.enums.SourceDataTypeEnum;
import com.izhonghong.ubc.information.common.enums.WebSpiderInformationSource;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.util.InformationSourceRequestBuilder;

/**
 * 信息源搜索使用示例
 * 展示如何使用新的POST请求方式搜索信息源
 * 
 * <AUTHOR>
 */
public class InformationSourceSearchExample {

    /**
     * 示例1：基本搜索
     */
    public void basicSearchExample() {
        try {
            // 创建信息源配置
            InformationSourceBean informationSourceBean = new InformationSourceBean();
            informationSourceBean.setServiceUrl("http://your-api-server/api/search");

            // 构建搜索条件
            JSONObject condition = InformationSourceRequestBuilder.condition()
                    .k3Id("12345")
                    .k3IdName("测试信息源")
                    .organizer("测试主办单位")
                    .name("测试账号")
                    .uid("test_uid_123")
                    .mediaInfoTag(0)  // 0: 非媒体, 1: 媒体
                    .mediaLevel("央级")  // 央级、省级、市级、区级、其他
                    .verifyType("政务")  // 政务、机构、企业、个人、未认证
                    .status(0)
                    .build();

            // 构建分页信息
            JSONObject paginator = InformationSourceRequestBuilder.paginator(0, 100)
                    .sorts("created_at", "desc")
                    .build();

            // 构建完整请求体
            JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(condition, paginator);

            // 调用新的搜索方法
            JSONObject result = WebSpiderInformationSource.INSTANCE
                    .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
                    .searchInformationSource(informationSourceBean, requestBody);

            System.out.println("搜索结果: " + result.toJSONString());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 示例2：微博相关搜索
     */
    public void weiboSearchExample() {
        try {
            InformationSourceBean informationSourceBean = new InformationSourceBean();
            informationSourceBean.setServiceUrl("http://your-api-server/api/search");

            // 构建微博相关搜索条件
            JSONObject condition = InformationSourceRequestBuilder.condition()
                    .name("微博账号名")
                    .weiboVerifyType("金V")  // 金V、橙V、黄V
                    .followersCountRange(1000, 100000)  // 粉丝数范围
                    .bigVLabel(1)  // 大V标签: 0=否, 1=是
                    .ipLocation("北京")
                    .area("北京市")
                    .build();

            JSONObject paginator = InformationSourceRequestBuilder.paginator(0, 50)
                    .sorts("followers_count", "desc")
                    .build();

            JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(condition, paginator);

            JSONObject result = WebSpiderInformationSource.INSTANCE
                    .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
                    .searchInformationSource(informationSourceBean, requestBody);

            System.out.println("微博搜索结果: " + result.toJSONString());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 示例3：行业分类搜索
     */
    public void industrySearchExample() {
        try {
            InformationSourceBean informationSourceBean = new InformationSourceBean();
            informationSourceBean.setServiceUrl("http://your-api-server/api/search");

            // 构建行业分类搜索条件
            JSONObject condition = InformationSourceRequestBuilder.condition()
                    .industry("科技行业")
                    .mediaLevel("省级")
                    .verifyType("企业")
                    .mediaInfoTag(1)  // 媒体类型
                    .status(0)  // 实施状态
                    .build();

            JSONObject paginator = InformationSourceRequestBuilder.paginator(0, 20)
                    .sorts("update_time", "desc")
                    .build();

            JSONObject requestBody = InformationSourceRequestBuilder.buildSearchRequest(condition, paginator);

            JSONObject result = WebSpiderInformationSource.INSTANCE
                    .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
                    .searchInformationSource(informationSourceBean, requestBody);

            System.out.println("行业搜索结果: " + result.toJSONString());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 示例4：直接使用JSON构建请求体
     */
    public void directJsonExample() {
        try {
            InformationSourceBean informationSourceBean = new InformationSourceBean();
            informationSourceBean.setServiceUrl("http://your-api-server/api/search");

            // 直接构建JSON请求体
            JSONObject requestBody = new JSONObject();
            
            JSONObject condition = new JSONObject();
            condition.put("k3Id", "XX");
            condition.put("mediaTag", "XX");
            condition.put("k3IdName", "XX信息源名字");
            condition.put("organizer", "XXX主办单位");
            condition.put("name", "账号名XXX");
            condition.put("uid", "XXXXX");
            condition.put("mediaInfoTag", 0);
            condition.put("mediaLevel", "央级");
            condition.put("weiboVerifyType", "金V");
            condition.put("verifyType", "政务");
            condition.put("industry", "XX行业");
            condition.put("followersCountRangeFrom", 0);
            condition.put("followersCountRangeTo", 1000);
            condition.put("bigVLabel", 1);
            condition.put("ipLocation", "XX地址");
            condition.put("area", "XX地址");
            condition.put("status", 0);

            JSONObject paginator = new JSONObject();
            paginator.put("from", 0);
            paginator.put("size", 100);
            
            JSONObject sort = new JSONObject();
            sort.put("field", "created_at");
            sort.put("order", "desc");
            
            JSONObject[] sorts = {sort};
            paginator.put("sorts", sorts);

            requestBody.put("condition", condition);
            requestBody.put("paginator", paginator);

            JSONObject result = WebSpiderInformationSource.INSTANCE
                    .getInstance(SourceDataTypeEnum.WEB_SPIDER.getCode())
                    .searchInformationSource(informationSourceBean, requestBody);

            System.out.println("直接JSON搜索结果: " + result.toJSONString());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
