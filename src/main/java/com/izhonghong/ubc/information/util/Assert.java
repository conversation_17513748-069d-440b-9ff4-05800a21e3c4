package com.izhonghong.ubc.information.util;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * 功能描述: 断言工具类，失败抛出AssertException
 * <AUTHOR>
 * @date 2019-06-27 10:53:11
 */
public final class Assert {

    /**
     * 功能描述: obj满足cpredicate
     * <AUTHOR>
     * @date 2019-06-27 13:10:27
     */
    public static <T> T require(T obj, Predicate<T> predicate, Supplier<String> messageSupplier) {
        if (predicate.test(obj)) {
            return obj;
        }
        throw new AssertException(messageSupplier.get());
    }

    /**
     * 功能描述: obj满足cpredicate
     * <AUTHOR>
     * @date 2019-06-27 13:10:27
     */
    public static <T> T require(T obj, Predicate<T> predicate, String message) {
        return require(obj, predicate, () -> message);
        
    }

    /**
     * 功能描述: condition为真
     * <AUTHOR>
     * @date 2019-06-27 13:10:27
     */
    public static void require(boolean condition, String message) {
        if (!condition) {
            throw new AssertException(message);
        }
    }

    /**
     * 功能描述: obj满足正则，使用matches判断
     * <AUTHOR>
     * @date 2019-06-27 13:21:37
     */
    public static <T> T require(T obj, String regex, String message) {
        return require(obj, tar -> Pattern.matches(regex, tar + ""), message);
    }

    /**
     * 功能描述: obj不是null
     * <AUTHOR>
     * @date 2019-06-27 13:37:18
     */
    public static <T> T requireNonNull(T obj, String message) {
        return require(obj, tar -> tar != null, message);
    }

    /**
     * 功能描述: obj非空，可以是Iterable、数组
     * <AUTHOR>
     * @date 2019-06-27 13:37:18
     */
    public static <T> T requireNonEmpty(T obj, String message) {
        if (obj instanceof Iterable) {
            Iterable iterable = (Iterable) obj;
            require(iterable != null && iterable.iterator().hasNext(), message);
            return obj;
        } else if (obj instanceof Object[]) {
            Object[] objArrray = (Object[]) obj;
            require(objArrray != null && objArrray.length > 0, message);
            return obj;
        }
        return require(obj, tar -> tar != null && !"".equals(tar), message);
    }

    /**
     * 功能描述: obj非空，包括trim后
     * <AUTHOR>
     * @date 2019-06-27 13:37:18
     */
    public static <T> T requireNonBlank(T obj, String message) {
        return require(obj, tar -> tar != null && !"".equals((tar + "").trim()), message);
    }

    /**
     * 功能描述: obj长度在[min, max]之间
     * <AUTHOR>
     * @date 2019-06-27 13:08:08
     */
    public static <T> T requireLengthBewteen(T obj, int min, int max, String message) {
        require(min >= 0 && min <= max, "min需要在区间[0, max]内");
        return require(obj, tar -> {
            int length = tar == null ? 0 : (tar + "").length();
            return length >= min && length <= max;
        }, message);
    }

    /**
     * 功能描述: obj包含target
     * <AUTHOR>
     * @date 2019-06-27 13:53:25
     */
    public static String requireContains(String obj, CharSequence target, String message) {
        return require(obj, tar -> tar != null && tar.contains(target), message);
    }

    /**
     * 功能描述: obj在collection中，使用== || equals判断
     * <AUTHOR>
     * @date 2019-06-27 13:08:29
     */
    public static <T> T requireIn(T obj, String message, T... collection) {
        return require(obj, tar -> Stream.of(collection).anyMatch(item -> item == tar || (item != null && item.equals(tar))), message);
    }

    /**
     * 功能描述: obj是一个数字
     * <AUTHOR>
     * @date 2019-06-27 13:24:06
     */
    public static <T> T requireNumber(T obj, String message) {
        return require(obj, "^-?\\d+(\\.\\d+)?$", message);
    }

    /**
     * 功能描述: obj满足pattern格式
     * <AUTHOR>
     * @date 2019-06-27 14:13:11
     */
    public static Date requireDateFormat(String obj, String pattern, String message) {
        try {
            return new SimpleDateFormat(pattern).parse(obj);
        } catch (ParseException e) {
            throw new AssertException(message);
        }
    }

    /**
     * 功能描述: obj是手机号
     * @date 2019/8/9
     * <AUTHOR>
     */
    public static String requirePhone(String obj, String message) {
        return require(obj, "^1[3456789]\\d{9}$", message);
    }

    /**
     * 功能描述: obj是邮箱
     * @date 2019/8/9
     * <AUTHOR>
     */
    public static String requireEmail(String obj, String message) {
        return require(obj, "^.+@.+\\..+$", message);
    }

    /**
     * 功能描述: condition为false时执行acceptor而不是抛出异常
     * @date 2019/7/18
     * <AUTHOR>
     */
    public static void failedAndThenAccept(boolean condition, FailedAcceptor acceptor) {
        if (!condition) {
            acceptor.accept();
        }
    }
    
    public static void assertTrue(String message, boolean condition) {
    	if (condition) {
    		 throw new AssertException(message);
    	}
    }
    
    public static void assertFlase(String message, boolean condition) {
    	assertTrue(message, !condition);
    }

    @FunctionalInterface
    public interface  FailedAcceptor{
        void accept();
    }

}

class AssertException extends RuntimeException {

    public AssertException(String message) {
        super(message);
    }
}

