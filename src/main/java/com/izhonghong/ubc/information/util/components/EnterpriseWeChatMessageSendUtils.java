package com.izhonghong.ubc.information.util.components;

import java.io.IOException;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.util.HttpRequest;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class EnterpriseWeChatMessageSendUtils {
	
	public static JSONObject sendMessage(String webHook,JSONObject message) throws IOException {
		
	    log.info("EnterpriseWeChatMessageSendUtils  sendMessage  webHook ={}  message = {} ",webHook,message);
		JSONObject sendResult= new HttpRequest(webHook).postJson(message).executeAsJsonObject();
		log.info("EnterpriseWeChatMessageSendUtils  sendResult === {} ",sendResult);
		
		return sendResult;
	}

}
