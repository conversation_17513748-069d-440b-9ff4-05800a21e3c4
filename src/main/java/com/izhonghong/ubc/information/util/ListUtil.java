package com.izhonghong.ubc.information.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

/**
 * @author:zyj
 * @version 创建时间：2016年8月16日 下午5:17:36 类说明
 */
public class ListUtil {
	
	public static <T> List<T> cutList(List<T> list, int from, int size) {
		int count = 0;
		if (!CollectionUtils.isEmpty(list)) {
			count = list.size();
			int fromIndex = from * size;
			int toIndex = (from + 1) * size;
			if(toIndex > count) {
				toIndex = count;
			}
			List<T> subList = list.subList(fromIndex, toIndex);
			return subList;
		}
		return new ArrayList<>();
	}
	
}
