package com.izhonghong.ubc.information.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 无限分类工具类
 * <AUTHOR>
 * @since 2018年12月11日 下午3:17:16
 */
public class TreeUtil {

	/**
	 * <p>把逻辑无限分类转换成树状无限分类</p>
	 * <p>待分类实体需要实现TreeAble接口</p>
	 * @param items 待分类元素，分类过程中，其中的元素会逐渐remove掉
	 * @param parent_id 根分类ID，不能由rootID寻找到的元素会遗留在items里
	 * @return
	 */
	public static List<? extends TreeAble> toTree(List<? extends TreeAble> items, Object parent_id) {
		List<TreeAble> children = new ArrayList<>();
		if (parent_id == null) {
			return children;
		}
		Iterator<? extends TreeAble> iterator = items.iterator();
		while (iterator.hasNext()) {
			TreeAble item = iterator.next();
			if (parent_id.equals(item.treePid())) {
				children.add(item);
				iterator.remove();
			}
		}
		for (TreeAble t : children) {
			t.treeChildren(toTree(items, t.treeId()));
		}
		return children;
	}
	
	/**
	 * <p>待分类实体需要实现的接口</p>
	 * <p>不以get/set开头以防干扰序列化/反序列化</p>
	 * <AUTHOR>
	 * @since 2018年12月11日 下午3:25:49
	 */
	public interface TreeAble<T> {
		Object treeId();
		Object treePid();
		void treeChildren(List<T> children);
	}
	
}


