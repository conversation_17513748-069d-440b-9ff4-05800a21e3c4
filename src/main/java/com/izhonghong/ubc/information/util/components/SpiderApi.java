package com.izhonghong.ubc.information.util.components;

import java.net.URLEncoder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.config.WebCrawlerConfig;
import com.izhonghong.ubc.information.util.HttpRequest;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SpiderApi {
	

	@Autowired
    private WebCrawlerConfig webCrawlerConfig;
	
	
	public JSONObject fastCrawlArticle(String url, int type,String originName) {
		JSONObject resultJson = new JSONObject();
		resultJson.put("code", "0");  
		try {
			String typeStr = "";
			if (type == 0) {
				typeStr = "微博";
			} else if (type == 2) {
				typeStr = "微信";
			}
			if("".equals(originName)) {
				originName = typeStr;
			}
			
			url = URLEncoder.encode(url,"utf-8");

			String apiUrl = webCrawlerConfig.getSpiderApiServer() + "/kys/input/article/info?url=%s&name=%s";
			apiUrl = String.format(apiUrl, url,originName);
			log.info("fastCrawlArticle apiUrl ====>{}",apiUrl);

			resultJson = new HttpRequest(apiUrl).get()
			        .executeAsJsonObject();
			resultJson.put("requestApiUrl", apiUrl);
			log.info("fastCrawlArticle resultJson ====>{}",resultJson);
		} catch (Exception e) {
			resultJson.put("message", e);
			log.error("=== fastCrawlArticle error.{}=== ",e);
			e.printStackTrace();
		}
		return resultJson;
	}
	
	
}
