package com.izhonghong.ubc.information.util;



import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.izhonghong.ubc.security.security.Authentication;
import com.izhonghong.ubc.security.security.TokenProvider;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

/**
 *
 * @Description: 用户工具类
 * <AUTHOR>
 * @date 2018-08-24
 * @version  1.0.0
 */
public class AppUserUtil {

	/**
	 * 获取用户名方法
	 *
	 * @return
	 */
	public static String getLoginName() {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		TokenProvider provider = TokenProvider.getInstance();
		String token = provider.getAccessToken(request);
		Authentication authentication = provider.getAuthentication(token);
		return authentication.getLoginName();
		/*if (authentication instanceof OAuth2Authentication) {
			OAuth2Authentication oAuth2Auth = (OAuth2Authentication) authentication;
			String loginName = oAuth2Auth.getUserAuthentication().getName();
			//User user11 = (User) oAuth2Auth.getUserAuthentication().getPrincipal();
			*//*if (authentication instanceof UsernamePasswordAuthenticationToken) {
				UsernamePasswordAuthenticationToken authenticationToken = (UsernamePasswordAuthenticationToken) authentication;
				Map map = (Map) authenticationToken.getDetails();
				User user=(User)authenticationToken.getPrincipal();
				System.out.println(authenticationToken.getPrincipal());
				map = (Map) map.get("principal");
				System.out.println("map="+map);
			}*//*
			return loginName;
		}*/
	}

	/**
	 * 功能描述: 从htttp请求头中获取token值
	 * <AUTHOR>
	 * @date 2019-06-17 15:08:50
	 */
	public static String getToken(HttpServletRequest request) {
		TokenProvider provider = TokenProvider.getInstance();
        String token = provider.getAccessToken(request);
		return Optional.ofNullable(token)
				.orElse("").replace("Bearer ", "");
	}

	public static String getCurrentToken() {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		TokenProvider provider = TokenProvider.getInstance();
		String token = provider.getAccessToken(request);
		return Optional.ofNullable(token)
				.orElse("").replace("Bearer ", "");
	}
}
