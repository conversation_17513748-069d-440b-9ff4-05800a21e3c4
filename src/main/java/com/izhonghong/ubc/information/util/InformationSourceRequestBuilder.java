package com.izhonghong.ubc.information.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.StringUtils;

/**
 * 信息源搜索请求体构建器
 * 用于构建POST请求的JSON请求体
 * 
 * <AUTHOR>
 */
public class InformationSourceRequestBuilder {

    /**
     * 构建信息源搜索请求体
     * 
     * @param condition 搜索条件
     * @param paginator 分页信息
     * @return JSON请求体
     */
    public static JSONObject buildSearchRequest(JSONObject condition, JSONObject paginator) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("condition", condition);
        requestBody.put("paginator", paginator);
        return requestBody;
    }

    /**
     * 构建搜索条件对象
     * 
     * @return 条件构建器
     */
    public static ConditionBuilder condition() {
        return new ConditionBuilder();
    }

    /**
     * 构建分页对象
     * 
     * @param from 起始位置
     * @param size 页面大小
     * @return 分页构建器
     */
    public static PaginatorBuilder paginator(int from, int size) {
        return new PaginatorBuilder(from, size);
    }

    /**
     * 条件构建器
     */
    public static class ConditionBuilder {
        private JSONObject condition = new JSONObject();

        public ConditionBuilder k3Id(String k3Id) {
            if (!StringUtils.isEmpty(k3Id)) {
                condition.put("k3Id", k3Id);
            }
            return this;
        }

        public ConditionBuilder mediaTag(String mediaTag) {
            if (!StringUtils.isEmpty(mediaTag)) {
                condition.put("mediaTag", mediaTag);
            }
            return this;
        }

        public ConditionBuilder k3IdName(String k3IdName) {
            if (!StringUtils.isEmpty(k3IdName)) {
                condition.put("k3IdName", k3IdName);
            }
            return this;
        }

        public ConditionBuilder organizer(String organizer) {
            if (!StringUtils.isEmpty(organizer)) {
                condition.put("organizer", organizer);
            }
            return this;
        }

        public ConditionBuilder name(String name) {
            if (!StringUtils.isEmpty(name)) {
                condition.put("name", name);
            }
            return this;
        }

        public ConditionBuilder uid(String uid) {
            if (!StringUtils.isEmpty(uid)) {
                condition.put("uid", uid);
            }
            return this;
        }

        public ConditionBuilder mediaInfoTag(Integer mediaInfoTag) {
            if (mediaInfoTag != null) {
                condition.put("mediaInfoTag", mediaInfoTag);
            }
            return this;
        }

        public ConditionBuilder mediaLevel(String mediaLevel) {
            if (!StringUtils.isEmpty(mediaLevel)) {
                condition.put("mediaLevel", mediaLevel);
            }
            return this;
        }

        public ConditionBuilder weiboVerifyType(String weiboVerifyType) {
            if (!StringUtils.isEmpty(weiboVerifyType)) {
                condition.put("weiboVerifyType", weiboVerifyType);
            }
            return this;
        }

        public ConditionBuilder verifyType(String verifyType) {
            if (!StringUtils.isEmpty(verifyType)) {
                condition.put("verifyType", verifyType);
            }
            return this;
        }

        public ConditionBuilder industry(String industry) {
            if (!StringUtils.isEmpty(industry)) {
                condition.put("industry", industry);
            }
            return this;
        }

        public ConditionBuilder followersCountRange(Integer from, Integer to) {
            if (from != null) {
                condition.put("followersCountRangeFrom", from);
            }
            if (to != null) {
                condition.put("followersCountRangeTo", to);
            }
            return this;
        }

        public ConditionBuilder bigVLabel(Integer bigVLabel) {
            if (bigVLabel != null) {
                condition.put("bigVLabel", bigVLabel);
            }
            return this;
        }

        public ConditionBuilder ipLocation(String ipLocation) {
            if (!StringUtils.isEmpty(ipLocation)) {
                condition.put("ipLocation", ipLocation);
            }
            return this;
        }

        public ConditionBuilder area(String area) {
            if (!StringUtils.isEmpty(area)) {
                condition.put("area", area);
            }
            return this;
        }

        public ConditionBuilder status(Integer status) {
            if (status != null) {
                condition.put("status", status);
            }
            return this;
        }

        public JSONObject build() {
            return condition;
        }
    }

    /**
     * 分页构建器
     */
    public static class PaginatorBuilder {
        private JSONObject paginator = new JSONObject();

        public PaginatorBuilder(int from, int size) {
            paginator.put("from", from);
            paginator.put("size", size);
        }

        public PaginatorBuilder sorts(String field, String order) {
            JSONArray sorts = paginator.getJSONArray("sorts");
            if (sorts == null) {
                sorts = new JSONArray();
                paginator.put("sorts", sorts);
            }
            
            JSONObject sort = new JSONObject();
            sort.put("field", field);
            sort.put("order", order);
            sorts.add(sort);
            
            return this;
        }

        public JSONObject build() {
            return paginator;
        }
    }
}
