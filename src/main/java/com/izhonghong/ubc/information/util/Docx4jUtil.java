package com.izhonghong.ubc.information.util;

import org.docx4j.TraversalUtil;
import org.docx4j.XmlUtils;
import org.docx4j.dml.CTNonVisualDrawingProps;
import org.docx4j.dml.CTPositiveSize2D;
import org.docx4j.dml.wordprocessingDrawing.Anchor;
import org.docx4j.dml.wordprocessingDrawing.Inline;
import org.docx4j.finders.RangeFinder;
import org.docx4j.openpackaging.exceptions.Docx4JException;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.BinaryPartAbstractImage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.org.apache.poi.util.IOUtils;
import org.docx4j.wml.*;
import org.springframework.util.FileCopyUtils;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.*;

public class Docx4jUtil {

    private WordprocessingMLPackage mlPackage;
    private ObjectFactory factory = new ObjectFactory();
    private static final String IMG_XPATH = "//w:drawing";

    public Docx4jUtil(File file) throws Docx4JException {
       mlPackage = WordprocessingMLPackage.load(file);
    }

    public static void createEmptyDocx(File file) throws Docx4JException {
        WordprocessingMLPackage aPackage = WordprocessingMLPackage.createPackage();
        aPackage.save(file);
    }

    public static void createOnlyPictureDocx(File file, Map<String, byte[]> imgs) throws Exception {
        Objects.requireNonNull(file);
        Objects.requireNonNull(imgs);
        WordprocessingMLPackage aPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart mainDocumentPart = aPackage.getMainDocumentPart();

        ObjectFactory factory = new ObjectFactory();
        addNewImgs(factory, aPackage, imgs);
        aPackage.save(file);
    }

    /**
     * 功能描述: 标记替换，标记的格式替换后也会存在
     * <AUTHOR>
     * @date 2019-06-29 17:30:12
     */
    public Docx4jUtil replaceTags(Map<String, String> tags) throws JAXBException, Docx4JException {
        mlPackage.getMainDocumentPart().variableReplace(tags);
        return this;
    }

    /**
     * 功能描述: 书签替换，可以在标签处插入文字或图片
     * <AUTHOR>
     * @date 2019-06-29 17:35:30
     */
    public Docx4jUtil replaceBookMarks(Map<String, Object> bookmarks) throws Exception {
        if (bookmarks == null) {
            return this;
        }
        Set<String> keySet = bookmarks.keySet();
        Body body = mlPackage.getMainDocumentPart().getJaxbElement().getBody();
        List<Object> paragraphs = body.getContent();
        RangeFinder rt = new RangeFinder("CTBookmark", "CTMarkupRange");
        new TraversalUtil(paragraphs, rt);
        for (CTBookmark bm : rt.getStarts()) {
            if (keySet.contains(bm.getName())) {
                Object value = bookmarks.get(bm.getName());
                if (value instanceof String || value instanceof Number) {
                    replaceText(bm, bookmarks.get(bm.getName()) + "");
                } else if (value instanceof File) {
                    addImg(bm, (File) value);
                }
            }
        }
        return this;
    }

    /**
     * 功能描述: 按图片descr替换图片，多增少删
     * <AUTHOR>
     * @date 2019-07-01 09:38:46
     */
    public Docx4jUtil replacePictures(Map<String, byte[]> pcitures) throws Exception {
        if (pcitures == null || pcitures.isEmpty()) {
            return this;
        }

        List<Object> drawingList = mlPackage.getMainDocumentPart().getJAXBNodesViaXPath(IMG_XPATH, true);
        for (Object content : drawingList) {
            JAXBElement<Drawing> drawingElement = (JAXBElement<Drawing>) content;
            Drawing drawing = drawingElement.getValue();
            List<Object> anchorOrInlines = drawing.getAnchorOrInline();
            for (int i = 0; i < anchorOrInlines.size(); i++) {
                Object anchorOrInline = anchorOrInlines.get(i);
                CTNonVisualDrawingProps docPr = null;
                CTPositiveSize2D extent = null;
                long yId = 0;
                if (anchorOrInline instanceof Anchor) {
                    Anchor anchor = (Anchor) anchorOrInline;
                    docPr = anchor.getDocPr();
                    yId = anchor.getGraphic().getGraphicData().getPic().getNvPicPr().getCNvPr().getId();
                    extent = anchor.getExtent();
                    /*Integer h = inline.getPositionH().getPosOffset();
                     Integer v = inline.getPositionV().getPosOffset();*/
                } else if (anchorOrInline instanceof Inline) {
                    Inline inline = (Inline) anchorOrInline;
                    docPr = inline.getDocPr();
                    extent = inline.getExtent();
                    yId = inline.getGraphic().getGraphicData().getPic().getNvPicPr().getCNvPr().getId();
                } else {
                    continue;
                }

                byte[] file = pcitures.get(docPr.getDescr());
                if (file == null) {
                    // pictures中没有的删掉
                    anchorOrInlines.remove(i);
                    continue;
                }
                BinaryPartAbstractImage imagePart = BinaryPartAbstractImage.createImagePart(mlPackage, file);
                Inline imageInline = imagePart.createImageInline(docPr.getName(), docPr.getDescr(),
                        (int) docPr.getId(), (int) yId, extent.getCx(), extent.getCy(), false);
                anchorOrInlines.set(i, imageInline);

                pcitures.remove(docPr.getDescr());
            }
        }
        // picutres有多的追加到尾部
        if (!pcitures.isEmpty()) {
            addNewImgs(factory, mlPackage, pcitures);
        }
        return this;
    }

    public void save(File file) throws Docx4JException {
        mlPackage.save(file);
    }

    public void save(OutputStream os) throws Docx4JException {
        mlPackage.save(os);
    }

    private static void addNewImgs(ObjectFactory factory, WordprocessingMLPackage mlPackage, Map<String, byte[]> imgs) throws Exception {
        MainDocumentPart mainDocumentPart = mlPackage.getMainDocumentPart();
        Iterator<Map.Entry<String, byte[]>> iterator = imgs.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, byte[]> img = iterator.next();
            BinaryPartAbstractImage imagePart = BinaryPartAbstractImage.createImagePart(mlPackage, img.getValue());
            Inline imageInline = imagePart.createImageInline(img.getKey(), img.getKey(),
                    0, 1, false, 0);
            Drawing drawing = factory.createDrawing();
            drawing.getAnchorOrInline().add(imageInline);

            R r = factory.createR();
            r.getContent().add(drawing);
            P p = mainDocumentPart.addParagraphOfText("");
            p.getContent().add(r);
        }
    }

    private void addImg(CTBookmark bookmark, File imgFile) throws Exception {
        P p = (P) bookmark.getParent();
        R r = factory.createR();
        byte[] bytes = IOUtils.toByteArray(new FileInputStream(imgFile));
        BinaryPartAbstractImage imagePart = BinaryPartAbstractImage.createImagePart(mlPackage, bytes);
        Inline imageInline = imagePart.createImageInline(imgFile.getName(), imgFile.getName(),
                0, 1, false, 0);
        Drawing drawing = factory.createDrawing();
        drawing.getAnchorOrInline().add(imageInline);

        r.getContent().add(drawing);
        p.getContent().add(r);
    }

    private void replaceText(CTBookmark bookmark, String text) {
        Object parent = bookmark.getParent();
        if (!(parent instanceof P)) {
            return;
        }
        P p = (P) parent;
        PPr pPr = p.getPPr();
        List<Object> contents = p.getContent();

        int rangeStart = -1;
        int rangeEnd = -1;
        int i = 0;
        for (Object content : contents) {
            Object entry = XmlUtils.unwrap(content);
            if (entry.equals(bookmark)) {
                if (((CTBookmark) entry).getName() != null) {
                    rangeStart = i + 1;
                }
            } else if (entry instanceof CTMarkupRange) {
                if (((CTMarkupRange) entry).getId().equals(bookmark.getId())) {
                    rangeEnd = i - 1;
                    break;
                }
            }
            i++;
        }
        int x = i - 1;
        for (int j = x; j >= rangeStart; j--) {
            contents.remove(j);
        }

        R r = factory.createR();
        Text t = factory.createText();
        t.setValue(text);
        r.getContent().add(t);
        contents.add(r);
    }
}
