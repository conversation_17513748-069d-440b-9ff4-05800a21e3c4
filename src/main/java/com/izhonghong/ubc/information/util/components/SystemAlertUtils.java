package com.izhonghong.ubc.information.util.components;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.izhonghong.ubc.information.common.enums.RobotTypeMenu;
import com.izhonghong.ubc.information.constant.SystemModuleEnum;
import com.izhonghong.ubc.information.entity.system.SystemAlert;
import com.izhonghong.ubc.information.factory.RobotMssageSendFactory;
import com.izhonghong.ubc.information.service.EnterpriseWechatRobotInforSet;
import com.izhonghong.ubc.information.service.abstracts.AbstractsRobotMssageSendService;
import com.izhonghong.ubc.information.service.system.SystemAlertService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SystemAlertUtils {
	
	
	@Autowired
	private SystemAlertService systemAlertService;
	/***
	 * 预警接口
	 * module 需要预警的模块
	 * message 预警信息 模块
	 * **/
	@Async
	public void warnInformation(SystemModuleEnum systemModuleEnum, String message) {
		String module = systemModuleEnum.code;
		message = systemModuleEnum.msg + message;
		
	   // 获取全局预警 和需要传入的module
		List<String> moduleList = new ArrayList<String>();
		moduleList.add(SystemModuleEnum.OPERATE_MODULE_GLOBAL.code);
		if(!StringUtils.isEmpty(module)) {
		  moduleList.add(module);
		}
		QueryWrapper<SystemAlert> query = new QueryWrapper<SystemAlert>();
		query.lambda().in(SystemAlert::getSystemModel,moduleList);
		List<SystemAlert> alertList = systemAlertService.list(query);
		SystemAlert systemAlert = new SystemAlert();
		
		//如果有两条，优先使用创建来的 否则使用全局的
		if(alertList != null && alertList.size() == 2 && !StringUtils.isEmpty(module)) {
			List<SystemAlert> moduleSystemAlert = alertList.stream().filter(t->module.equals(t.getSystemModel())).collect(Collectors.toList());
			if(moduleSystemAlert != null && moduleSystemAlert.size() > 0) {
				systemAlert = moduleSystemAlert.get(0);
			}
		}
		if(alertList != null && (systemAlert == null || StringUtils.isEmpty(systemAlert.getSystemModel()))) {
			systemAlert = alertList.get(0);
		}
		//数据推送
		qyWechart(systemAlert,message);
	}
	
	/***
	 * 企业微信信息发送
	 * **/
	public void qyWechart(SystemAlert systemAlert, String content) {
		String webHook = systemAlert.getWebhook();
		if(systemAlert != null  && !StringUtils.isEmpty(webHook)) {
			 log.info("SystemAlertService === webHook ={}  content = {}",webHook,content);
	    	 if(StringUtils.isEmpty(webHook) || StringUtils.isEmpty(content)) {
	    		 log.info("SystemAlertService params error === webHook ={}  content = {}",webHook,content);
	    		 return;
	    	 }
	    	 EnterpriseWechatRobotInforSet setInfor = new EnterpriseWechatRobotInforSet(content, webHook, new String[] {"@all"});
	    	 AbstractsRobotMssageSendService robotService = RobotMssageSendFactory.creatRobot(RobotTypeMenu.EE_WE_CHAT.getCode());
	    	 
			 try {
				robotService.sendTextMessage(setInfor);
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}
		
	}

	
}
