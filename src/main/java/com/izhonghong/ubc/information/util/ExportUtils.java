package com.izhonghong.ubc.information.util;

import com.izhonghong.ubc.information.constant.SystemModuleEnum;
import com.izhonghong.ubc.information.util.components.SystemAlertUtils;

import cn.afterturn.easypoi.entity.ImageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.stream.FileImageInputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
@Slf4j
public class ExportUtils {
    @Autowired
    private SystemAlertUtils systemAlertUtils;

    /***
     * 从服务器下载文件
     * rootPath 文件路径
     * response HttpServletResponse
     * fileName 文件名 包含后缀
     * **/
    public  Boolean exportDocument(String rootPath,String fileName, HttpServletResponse response){
        File packetFile = new File(rootPath);
        String fn = packetFile.getName(); //下载的文件名
        File file = new File(rootPath);
        
        InputStream  fis = null;
        boolean exportflag = false;
        try {
        	response.reset();
            // 如果文件名存在，则进行下载
            if (file.exists()) {
                // 配置文件下载
                response.setHeader("content-type", "application/octet-stream");
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");
                // 下载文件能正常显示中文
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                // 实现文件下载
                byte[] buffer = new byte[1024];

                fis = new FileInputStream(file);
                
                int i ;
                while ((i=fis.read(buffer))>0) {
                	response.getOutputStream().write(buffer, 0, i);
                }
                exportflag = true;
                log.info("Download the song successfully!");
            }
        } catch (Exception e) {
            log.error("Download the song failed!");
            systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_MEDIA,"导入模板下载失败，请检查"+e);
        }finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
				response.getOutputStream().flush();
			} catch (IOException e) {
				e.printStackTrace();
			}
            try {
				response.getOutputStream().close();
			} catch (IOException e) {
				e.printStackTrace();
			}
        }
        
        return exportflag;
    }

    /***
     * 删除临时文件
     * rootPath 文件路径
     * **/
    public void deleteTempDocument(String rootPath){
        try {
            File directoryPath = new File(rootPath);
            directoryPath.delete();
        }catch (Exception e){
            systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL,"临时文件导出失败，请检查"+e);
            log.error("deleteTempDocument error{}",e);
        }
    }
    
    /***
     * 删除临时文件
     * rootPath 文件路径
     * **/
    public void cleanDocument(String rootPath){
        try {
        	File file = new File(rootPath);
    		if(file.isFile()) {
    			file.delete();
    		}else {
    			File[] files = file.listFiles();
    			if(files == null) {
    				file.delete();
    			}else {
    				for (int i = 0; i < files.length; i++)  {
    					cleanDocument(files[i].getAbsolutePath());
    				}
    				file.delete();
    			}
    		}
        }catch (Exception e){
            systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL,"临时文件导出失败，请检查"+e);
            log.error("deleteTempDocument error{}",e);
        }
    }
    /**
     * 创建目录
     * **/
    public void bulidDocument(String path) {
    	File file = new File(path);
    	if(!file.getParentFile().exists()){//如果文件夹不存在
			file.getParentFile().mkdir();//创建文件夹
		}
    }


    /***
     * 下载文件服务器的文件
     * urlString 文件位置
     * filename 临时文件的名称
     * savePath 文件的存储地址
     * **/
    public boolean download(String urlString, String filename,String savePath) {

            boolean downloadFlag = false;
            // 输入流
            InputStream is = null;
            OutputStream os = null;
            try {
                // 构造URL
                URL url = new URL(urlString);

                // 打开连接
                URLConnection con = url.openConnection();
                //设置请求超时为5s
                con.setConnectTimeout(5*1000);

                is = con.getInputStream();
                // 1K的数据缓冲
                byte[] bs = new byte[1024];
                // 读取到的数据长度
                int len;
                // 输出的文件流
                File sf = new File(savePath);
                if (!sf.exists()) {
                    sf.mkdirs();
                }
                 os = new FileOutputStream(sf.getPath() +File.separator + filename);
                // 开始读取
                while ((len = is.read(bs)) != -1) {
                    os.write(bs, 0, len);
                }
                downloadFlag = true;
             }catch (Exception e){
                systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL,"远程下载临时文件失败，请检查"+e);
                log.error("ExportUtils download error.{}",e);
             }finally {
                try {
                    // 完毕，关闭所有链接
                    if (os != null) {
                        os.close();
                    }
                    if (is != null) {
                        is.close();
                    }
                }catch (IOException e){
                    systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL,"远程下载临时文件IO关闭失败，请检查"+e);
                    log.error("ExportUtils download close IO error.{}",e);
                }

             }
         return downloadFlag;
    }
    
    //图片到byte数组
    public byte[] image2byte(String path){
        byte[] data = null;
        FileImageInputStream input = null;
        try {
            input = new FileImageInputStream(new File(path));
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int numBytesRead = 0;
            while ((numBytesRead = input.read(buf)) != -1) {
                output.write(buf, 0, numBytesRead);
            }
            data = output.toByteArray();
            output.close();
            input.close();
        }
        catch (FileNotFoundException ex1) {
            ex1.printStackTrace();
        }
        catch (IOException ex1) {
            ex1.printStackTrace();
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    log.error("image2byte close IO error.{}",e);
                    e.printStackTrace();
                }
            }
        }
        return data;
    }
    
    
    //生成easypoi需要的图片
    public ImageEntity getWordImageEntity(byte[] imgdata,int height,int width) {
    	ImageEntity  imageEntity = new ImageEntity () ;
        imageEntity.setHeight(height);
        imageEntity.setWidth(width);

        imageEntity.setData(imgdata);
        imageEntity.setType(imageEntity.Data);
        return imageEntity;
    }
    
    
    
    /***
     * 下载文件服务器的文件
     * urlString 文件位置
     * filename 临时文件的名称
     * savePath 文件的存储地址
     * **/
    public boolean downloadByByte(byte[] bs, String filename,String savePath) {

            boolean downloadFlag = false;
            // 输入流
            OutputStream os = null;
            try {
//                // 读取到的数据长度
//                int len;
                // 输出的文件流
                File sf = new File(savePath);
                if (!sf.exists()) {
                    sf.mkdirs();
                }
                 os = new FileOutputStream(sf.getPath() +File.separator + filename);
                // 开始读取
//                while ((len = is.read(bs)) != -1) {
                    os.write(bs);
//                }
                downloadFlag = true;
             }catch (Exception e){
                systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL," 远程下载临时文件失败，请检查"+e);
                log.error("ExportUtils download error.{}",e);
             }finally {
                try {
                    // 完毕，关闭所有链接
                    if (os != null) {
                        os.close();
                    }
                }catch (IOException e){
                    systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL," 远程下载临时文件IO关闭失败，请检查"+e);
                    log.error("ExportUtils download close IO error.{}",e);
                }

             }
         return downloadFlag;
    }

    
    public static void compressionFile(String path, String zipFilePath) {
        File file = new File(path);
        if(file == null || !file.exists() || !file.isDirectory()){
            return;
        }
        File zipFile = new File(zipFilePath);
        File[] srcFile = file.listFiles();
        byte[] buffer = new byte[1024];
        ZipOutputStream out = null;
        FileInputStream fileInputStream = null;
        try {
             out = new ZipOutputStream(new FileOutputStream(zipFile));
            for (int i = 0; i < srcFile.length; i++) {
                fileInputStream = new FileInputStream(srcFile[i]);
                out.putNextEntry(new ZipEntry(srcFile[i].getName()));
                int length;
                while ((length = fileInputStream.read(buffer)) > 0) {
                    out.write(buffer, 0, length);
                }
                out.closeEntry();
            }
            
        } catch (Exception e) {
        	log.error("compressionFile error.{}",e);
            e.printStackTrace();
        }finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("compressionFile close IO error.{}",e);
                    e.printStackTrace();
                }
            }
        	if (out != null) {
        		try {
					out.close();
				} catch (IOException e) {
					log.error("compressionFile close IO error.{}",e);
					e.printStackTrace();
				}
			}
        }
    }



}
