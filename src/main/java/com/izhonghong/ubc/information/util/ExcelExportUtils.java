package com.izhonghong.ubc.information.util;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

public class ExcelExportUtils {
	

	/**
	 * 导出Excel
	 * 
	 * @param sheetName sheet名称
	 * @param title     标题
	 * @param values    内容
	 * @param request 
	 * @return
	 */
	public static void createWorkbook(String fileName,String sheetName, String[] title, String[][] values,HttpServletResponse response, HttpServletRequest request) {

		// 第一步，创建一个HSSFWorkbook，对应一个Excel文件
		HSSFWorkbook wb = new HSSFWorkbook();

		// 第四步，创建单元格，并设置值表头 设置表头居中
		HSSFCellStyle style = wb.createCellStyle();
		style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 创建一个居中格式
	
		HSSFSheet sheet = wb.createSheet(sheetName);
		HSSFSheet sheet1 = wb.createSheet("sheet");
		HSSFRow row = sheet.createRow(0);

		// 声明列对象
		HSSFCell cell = null;

		// 创建标题
		for (int i = 0; i < title.length; i++) {
			cell = row.createCell(i);
			cell.setCellValue(title[i]);
			cell.setCellStyle(style);
		}

		// 创建内容
		for (int i = 0; i < values.length; i++) {
			row = sheet.createRow(i + 1);
			for (int j = 0; j < values[i].length; j++) {
				// 将内容按顺序赋给对应的列对象
				row.createCell(j).setCellValue(values[i][j]);
			}
		}
		 OutputStream os = null;
		try {
            //设置编码、输出文件格式
            response.reset();
            java.net.URLEncoder.encode(sheetName, "UTF-8");
            String userAgent = request.getHeader("user-agent");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls","UTF-8"));
            os = response.getOutputStream();
            wb.write(os);
          
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
        	 try {
        		  if(os != null) {
					os.flush();
					os.close();
        		  }
			} catch (IOException e) {
				e.printStackTrace();
			}
        	  
           try {
  				wb.close();
  			} catch (IOException e) {
  				e.printStackTrace();
  			}
             
        }
		
	}
	
}
