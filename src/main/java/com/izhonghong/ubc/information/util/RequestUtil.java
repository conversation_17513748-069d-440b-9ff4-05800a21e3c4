package com.izhonghong.ubc.information.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.net.URLEncoder;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 功能描述: 主要用于查询参数处理
 * <AUTHOR>
 * @date 2019-06-05 14:27:57
 */
public class RequestUtil {

    /**
     * 功能描述: map过滤
     * <AUTHOR>
     * @date 2019-06-05 14:28:47
     */
    public static Map<String, Object> filter(Map<String, Object> map, Predicate<Map.Entry> predicate) {
        if (map == null || predicate == null) {
            return map;
        }
        return map.entrySet()
                .stream()
                .filter(predicate)
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
    }

    /**
     * 功能描述: 过滤掉map中所有value为null或""的元素
     * <AUTHOR>
     * @date 2019-06-05 14:29:07
     */
    public static Map<String, Object> filterEmptyValue(Map<String, Object> map) {
        if (map == null) {
            return map;
        }
        return map.entrySet()
                .stream()
                .filter(entry -> isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
    }

    /**
     * 功能描述: 过滤掉map中所有key不在keys中，或value为null或""的元素
     * <AUTHOR>
     * @date 2019-06-05 14:29:48
     */
    public static Map<String, Object> filterKeyAndValue(Map<String, Object> map, String[] keys) {
        if (map == null) {
            return map;
        }
        Set<String> keySet = Stream.of(keys).collect(Collectors.toSet());
        return map.entrySet()
                .stream()
                .filter(entry -> keySet.contains(entry.getKey()) && isNotEmpty(entry.getValue()))
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
    }

    /**
     * 功能描述: 初始化mybatis-plus分页参数
     * <AUTHOR>
     * @date 2019-06-05 15:33:38
     */
    public static Page initPage(int current, int size) {
        Page page = new Page();
        if (current < 1) {
            current = 1;
        }
        if (size < 1) {
            size = 10;
        }
        page.setCurrent(current);
        page.setSize(size);
        return page;
    }

    /**
     * 功能描述: 初始化mybatis-plus分页参数
     * <AUTHOR>
     * @date 2019-06-05 15:34:09
     */
    public static Page initPage(Map<String, Object> params, String pageNoKey, String pageSizeKey) {
        int current = 1, size = 10;
        if (params != null) {
            if (params.containsKey(pageNoKey)) {
                current = (int) params.get(pageNoKey);
            }
            if (params.containsKey(pageSizeKey)) {
                size = (int) params.get(pageSizeKey);
            }
        }
        return initPage(current, size);
    }

    /**
     * 功能描述: 驼峰转下划线
     * <AUTHOR>
     * @date 2019-06-05 18:13:45
     */
    public static String camelCaseToUnderCase(String value) {
        return Optional.ofNullable(value).orElse("").replaceAll("([A-Z])", "_$1").toLowerCase();
    }

    /**
     * 功能描述: 下划线转驼峰
     * <AUTHOR>
     * @date 2019-06-05 18:13:57
     */
    public static String underCaseToCamelCase(String value) {
        String[] array = Optional.ofNullable(value).orElse("").split("_");
        return array[0] + Stream.of(array)
                .skip(1)
                .map(v -> isEmpty(v) ? "" : Character.toUpperCase(v.charAt(0)) + v.substring(1))
                .reduce((a, b) -> a + b)
                .orElse("");
    }

    /**
     * 功能描述: 判断value是null或""
     * <AUTHOR>
     * @date 2019-06-05 18:15:43
     */
    public static boolean isEmpty(Object value) {
        return "".equals(Optional.ofNullable(value).orElse(""));
    }

    /**
     * 功能描述: 判断value不是null和""
     * <AUTHOR>
     * @date 2019-06-05 18:15:43
     */
    public static boolean isNotEmpty(Object value) {
        return !isEmpty(value);
    }

    /**
     * 功能描述: 判断value是null或空串
     * <AUTHOR>
     * @date 2019-06-05 18:15:43
     */
    public static boolean isBlank(Object value) {
        return "".equals(Optional.ofNullable(value).orElse("").toString().trim());
    }

    /**
     * 功能描述: 判断value不是null和空串
     * <AUTHOR>
     * @date 2019-06-05 18:15:43
     */
    public static boolean isNotBlank(Object value) {
        return !isBlank(value);
    }

    /**
     * 功能描述: map转rul参数
     * <AUTHOR>
     * @date 2019-06-13 15:42:41
     */
    public static String serialize(Map<String, Object> params) {
        StringBuilder s = new StringBuilder();
        if (params == null) {
            return s.toString();
        }
        Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            s.append("&" + entry.getKey() + "=" + Optional.ofNullable(entry.getValue()).orElse(""));
        }
        String url = s.toString().replaceFirst("&", "?");
        return URLEncoder.encode(url);
    }

}
