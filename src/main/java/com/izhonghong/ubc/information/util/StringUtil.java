package com.izhonghong.ubc.information.util;

import com.izhonghong.ubc.inf.util.text.Md5Utils;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.security.SecureRandom;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 字符串工具类
 */
public class StringUtil {

    private static Pattern PATTERN = Pattern.compile("<[^>]*>");

    private static Pattern PATTERN_2 = Pattern.compile("^[-\\+]?[\\d]*$");

    public static String getFileName(String fileName) {
        String name = null;
        if (!isEmpty(fileName)) {
            name = fileName.substring(fileName.lastIndexOf("."));
        }
        return name;
    }

    public static boolean isStrNull(String str) {
        return str == null || str.trim().length() == 0;
    }

    public static boolean isStrArrayNull(String[] strArr) {
        return strArr == null || strArr.length == 0;
    }

    public static boolean isMapNull(Map map) {
        return map == null || map.isEmpty();
    }

    public static boolean isListNull(List list) {
        return list == null || list.isEmpty();
    }

    /**
     * 替换掉html字符成空
     *
     * @param string 待替换的字符串
     * @return 替换后的字符串
     */
    public static String replaceHtmlTag(String string) {
        if (isStrNull(string)) {
            return string;
        }

        String temp = string.replaceAll("&lt;", "<").replaceAll("&gt;", ">")
                .replaceAll("&#60;", "<").replaceAll("&#62;", ">").replaceAll("&nbsp;", "")
                .replaceAll(">", "")
                .replaceAll("<", "")
                .replaceAll(" ", "")
                .replaceAll("\"", "")
                .replaceAll("\'", "")
//		.replaceAll("\\", "\\\\")//对斜线的转义
                .replaceAll("\n", "")
                .replaceAll("\r", "");

        Matcher matcher = PATTERN.matcher(temp);
        return matcher.replaceAll("");
    }


    public static String removeTag(String html) {
        if (html == null) {
            return null;
        }
        StringBuffer strb = new StringBuffer(html.length());
        int begin = 0;
        int p = html.indexOf("<");
        while (p != -1 && p < html.length() - 1) {
            if (html.charAt(p + 1) < 128) {
                int p1 = html.indexOf(">", p + 1);
                int p2 = html.indexOf("<", p + 1);
                if (p1 != -1 && (p1 < p2 || p2 == -1)) {
                    strb.append(html.substring(begin, p));
                    begin = p1 + 1;
                }
                p = p2;
            } else {
                p = html.indexOf("<", p + 1);
            }

        }
        strb.append(html.substring(begin));
        String str = strb.toString();
        str = Convert.replace(str, "&nbsp;", " ");
        str = Convert.replace(str, "&amp;", "&");
		/*str = Convert.replace(str, "&lt;", "<");
		str = Convert.replace(str, "&gt;", ">");*/
        str = Convert.replace(str, "<", "&lt;");
        str = Convert.replace(str, ">", "&gt;");
        str = Convert.replace(str, "&brvbar;", "|");
        str = Convert.replace(str, "&quot;", "\"");
        str = Convert.replace(str, "&middot;", "·");
        str = Convert.replace(str, "&bull;", "·");
        return trim(str);
    }


    public static String trim(String in) {
        if (in == null || in.length() == 0) {
            return "";
        }
        char[] charArray = in.toCharArray();
        StringBuffer strBuff = new StringBuffer(in.length());
        boolean nPara = true;
        for (int i = 0; i < charArray.length; i++) {
            char c = charArray[i];
            switch (c) {
                case '\r':
                    nPara = true;
                    break;
                case ' ':
                case '　':
                    if (nPara) {
                        ;
                    } else {
                        strBuff.append(c);
                    }
                    break;
                case '\n':
                    nPara = true;
                    break;
                default:
                    strBuff.append(c);
                    if (nPara) {
                        nPara = false;
                    }
            }
        }
        return strBuff.toString();
    }


    /**
     * 替换掉sql关键字符成空, 替换后会将原来字符串中的的英文字母转换成小写
     *
     * @param string 待替换的字符串
     * @return 替换后的字符串
     */
    public static String replaceSqlTag(String string) {
        if (isStrNull(string)) {
            return string;
        }
        String sqltags[] = {"and", "exec", "insert", "select", "delete",
                "update", "count", "%", "*", "chr", "mid", "master",
                "truncate", "char", "declare"};
        String temp = string.toLowerCase();
        for (int i = 0; i < sqltags.length; i++) {
            if (temp.indexOf(sqltags[i]) != -1) {
                temp = temp.replaceAll(sqltags[i], "");
            }
        }
        return temp;

    }

    /**
     * 字符串编码转换，如果转码失败则返回原字符串
     *
     * @param str             待转码的字符串
     * @param oldCharasetCode 旧字符编码
     * @param newCharasetCode 新字符编码
     * @return 返回转码后的字符串
     */
    public static String charactorCodeConversion(String str,
                                                 String oldCharasetCode, String newCharasetCode) {
        String tempStr;
        try {
            tempStr = new String(str.getBytes(oldCharasetCode), newCharasetCode);
        } catch (UnsupportedEncodingException e) {
            tempStr = str;
        }
        return tempStr;
    }

    /**
     * 将Null替换成空字符串
     *
     * @param str
     * @return
     */
    public static String replaceNull(String str) {
        return str == null ? "" : str;
    }

    /**
     * 返回字符串s中字母c出现的个数no
     *
     * @param s
     * @param c
     * @return
     */
    public static int getCharCountInStr(String s, char c) {
        // 返回字符串s中字母c出现的个数no；
        int no = 0;
        char[] cs = s.toCharArray();
        for (int i = 0; i < cs.length; i++) {
            if (c == cs[i]) {
                no++;
            }
        }
        return no;
    }

    /**
     * 判断字符串为空或者长度是否等于0
     *
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        return "".equals(clear(str));
    }

    /**
     * 判断字符串值不为空
     *
     * @param str
     * @return
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 去掉字符串前后的空格，如果为空则返回""
     *
     * @param a
     * @return
     */
    public static String clear(String a) {
        return nvl(a, "").trim();
    }

    /**
     * 判断参数a是否为空，如果为空则返回b (适用与字符串等所有对象)
     *
     * @param <K>
     * @param a
     * @param b
     * @return
     */
    public static <K> K nvl(K a, K b) {
        return a == null ? b : a;
    }

    /**
     * 判断是否为数字
     *
     * @param s
     * @return
     */
    public static boolean isNum(String s) {
        if (s == null) {
            return true;
        }
        char[] c = s.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if ((c[i] >= '0' && c[i] <= '9')) {
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 去除String[] 数组的空白位置
     *
     * @param obj
     * @return
     */
    public static String[] trim(String[] obj) {
        String[] r = obj;
        int num = 0;
        for (int i = 0; i < r.length; i++) {
            if (r[i] == null || r[i].trim().length() < 1) {
                num++;
            }
        }
        if (num > 0) {
            r = new String[r.length - num];
            int j = 0;
            for (int i = 0; i < obj.length; i++) {
                if (obj[i] != null && obj[i].trim().length() > 0) {
                    r[j++] = obj[i].trim();
                }
            }
        }
        return r;
    }

    /**
     * 判断目标字符串是否是源字符以逗号分隔后的一个子串
     *
     * @param source
     * @param target
     * @return
     */
    public static boolean contains(String source, String target) {
        if (source != null && source.length() > 0) {
            String[] all = source.split(",");
            for (String str : all) {
                if (str.trim().equals(target)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断目标字符串是否是源字符以逗号分隔后的一个子串
     *
     * @param source
     * @param target
     * @return
     */
    public static boolean containsi(String source, String target) {
        if (source != null && source.length() > 0) {
            String[] all = source.split("@");
            for (String str : all) {
                if (str.trim().equals(target)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 生成流水号 如 0005
     *
     * @param length 生成出来的总长度
     * @param value  值，不填则默认为0
     * @return (4, null) 返回 0001,在value的基础上加1
     */
    public static String makeNumber(int length, String value) {
        if (isStrNull(value)) {
            value = "0";
        }
        Integer newValue = Integer.parseInt(value) + 1;
        String strValue = newValue.toString();
        length = length - strValue.length();
        if (length > 0) {
            String first = "";
            for (int i = 0; i < length; i++) {
                first += "0";
            }
            strValue = first + strValue;
        }
        return strValue;
    }

    //以指定长度分隔字符串
    public static String[] split(String msg, int num) {
        int len = msg.length();
        if (len <= num) {
            return new String[]{msg};
        }
        int count = len / (num - 1);
        count += len > (num - 1) * count ? 1 : 0; // 这里应该值得注意
        String[] result = new String[count];
        int pos = 0;
        int splitLen = num - 1;
        for (int i = 0; i < count; i++) {
            if (i == count - 1) {
                splitLen = len - pos;
            }

            result[i] = msg.substring(pos, pos + splitLen);
            pos += splitLen;

        }
        return result;
    }

    /**
     * 从sql语句中选取":"参数
     *
     * @param sql
     * @return
     */
    public static Set<String> pickSqlParams(String sql) {
        Set<String> params = new HashSet<String>();
        int beginPos = sql.indexOf(":");
        int endPos = 0;
        while (beginPos >= 0) {
            endPos = sql.indexOf(" ", beginPos);
            if (endPos < 0) {
                endPos = sql.length();
            }
            String paramName = sql.substring(beginPos + 1, endPos);
            beginPos = sql.indexOf(":", endPos);
            params.add(paramName);
        }
        return params;
    }


    /**
     * 字符串的首字符小写
     *
     * @param original 原字符串
     * @return 结果字串
     */
    public static String lowerCaseFirstCharacter(String original) {
        if (original == null) {
            return original;
        }
        if (original.equals("")) {
            return original;
        }
        char[] chrs = original.toCharArray();
        chrs[0] = Character.toLowerCase(chrs[0]);
        return new String(chrs);
    }

    private static SecureRandom random = new SecureRandom();
    private static char[] num = new char[]{'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9'};

    /**
     * 取随机的字符串不含字母
     *
     * @param length
     * @return
     */
    public static String getRandomNum(int length) {
        char[] r = new char[length];
        for (int i = 0; i < r.length; i++) {
            r[i] = num[random.nextInt(num.length)];
        }
        return String.valueOf(r);
    }

    /**
     * 转为sql语句 'data1','data2'
     *
     * @param data
     * @return
     */
    public static String sqlIn(String data, String regex) {
        return sqlIn(data.split(regex));
    }

    /**
     * 默认按照,分割 转为sql语句 'data1','data2'
     *
     * @param data
     * @return
     */
    public static String sqlIn(String data) {
        return sqlIn(data.split(","));
    }

    /**
     * 数组
     *
     * @param datas
     * @return
     */
    public static String sqlIn(String[] datas) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < datas.length; i++) {
            sb.append("'");
            sb.append(datas[i]);
            sb.append("'");
            if (i < datas.length - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static int statWord(String keyword) {
        int c = 0;
        if (StringUtils.isNotBlank(keyword)) {
            String[] words = keyword.split("[(|)|!|+|*|-| ]+");
            for (int i = 0; i < words.length; i++) {
                String word = words[i].replaceAll("[(|)|!|+|*|-]", "");
                if (StringUtils.isNotBlank(word)) {
                    //System.out.print(word + ",");
                    c++;
                }
            }
            //System.out.println(c);
        }
        return c;
    }


    public static boolean valilenthForString(String str, int lenth) {
        if (str != null && !str.equals("")) {
            if (str.length() > lenth) {

                return false;
            } else {
                return true;
            }

        } else {
            return false;
        }
    }

    /**
     * 阿拉伯数字转汉语数字
     *
     * @param number
     * @return <br/>Success:
     * <br/>Error:
     * <br/>Author: Lisw
     * <br/>Date: 2018年5月7日 下午1:40:13
     * <br/>URL:
     */
    public static String numConvertChineseNum(int number) {

        //数字对应的汉字
        String[] num = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        //单位
        String[] unit = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万亿"};

        //将输入数字转换为字符串
        String result = String.valueOf(number);
        //将该字符串分割为数组存放
        char[] ch = result.toCharArray();
        //结果 字符串
        StringBuffer str = new StringBuffer("");
        int length = ch.length;
        for (int i = 0; i < length; i++) {
            int c = (int) ch[i] - 48;
            if (c != 0) {
                str.append(num[c]).append(unit[length - i - 1]);
            } else {
                str.append(num[c]);
            }
        }
        return str.toString();
    }


    public static String getUid(String siteId, String biz) {
        String uid = siteId + "_" + biz;
        String u = Md5Utils.encode(uid).substring(0, 4);
        System.out.println(u + uid);
        return u + uid;
    }


    public static String getRealWord(String keyword) {
        String keywords = "";
        if (StringUtils.isNotBlank(keyword)) {
            String[] words = keyword.split("[(|)|!|+|*|-| ]+");
            for (int i = 0; i < words.length; i++) {
                String word = words[i].replaceAll("[(|)|!|+|*|-]", "");
                if (StringUtils.isNotBlank(word) && word.length() > 1) {
                    keywords += "+" + word;
                }
            }
        }
        if (StringUtils.isNotBlank(keywords)) {
            keywords = keywords.substring(1);
        }
        return keywords;
    }

    private static boolean isEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA)
                || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF))
                || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD))
                || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
    }

    public static String filterEmoji(String source) {
        if (StringUtils.isBlank(source)) {
            return source;
        }
        StringBuilder buf = null;
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (isEmojiCharacter(codePoint)) {
                if (buf == null) {
                    buf = new StringBuilder(source.length());
                }
                buf.append(codePoint);
            }
        }
        if (buf == null) {
            return source;
        } else {
            if (buf.length() == len) {
                buf = null;
                return source;
            } else {
                return buf.toString();
            }
        }
    }

    public static String aritcleType2Str(int media) {
        switch (media) {
            case 0:
                return "微博";
            case 1:
                return "新闻网站";
            case 2:
                return "微信";
            case 3:
                return "论坛";
            case 4:
                return "博客";
            case 5:
                return "报纸";
            case 6:
                return "视频";
            case 7:
                return "QQ";
            case 8:
                return "跟帖";
            case 9:
                return "境外";
            case 10:
                return "Twitter";
            case 11:
                return "新闻客户端";
            case 15:
                return "电报";
            case 16:
                return "Youtube";
            default:
                return null;
        }
    }

    public static boolean isOverSeas(Integer mediaType) {
        return ((Integer) 9).equals(mediaType) || ((Integer) 10).equals(mediaType);
    }


    /**
     * 判断是否为链接地址
     *
     * @param url
     * @return
     */
    public static boolean isHttpUrl(String url) {
        String regex = "^https?://([\\w-]+\\.)+[\\w-]+(/[\\u4E00-\\u9FA5\\w- ./?%&=#]*)?";
        Pattern pat = Pattern.compile(regex.trim());//比对
        Matcher mat = pat.matcher(url.trim());
        boolean isurl = mat.matches();//判断是否匹配
        return isurl;
    }

    //过滤包含特殊字符的关键词
    public static Map<String, Integer> cleaSpecialValue(Map<String, Integer> map) {
        Map<String, Integer> reMap = new HashMap<String, Integer>();
        String regex = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t|\\n";//设置正则表达式
        Pattern p = Pattern.compile(regex);
        Iterator<Map.Entry<String, Integer>> it = map.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Integer> entry = it.next();
            String key = entry.getKey();
            if (!p.matcher(key).find()) {
                reMap.put(key, entry.getValue());
            }
        }
        return reMap;

    }

    //排序
    public static LinkedMap<String, Integer> sortForValue(Map<String, Integer> map, int getCount) {
        int size = map.size();
        //通过map.entrySet()将map转换为"1.B.1.e=78"形式的list集合
        List<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(size);
        list.addAll(map.entrySet());
        //通过Collections.sort()排序
        Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                return o2.getValue().compareTo(o1.getValue());
            }

            ;
        });
        LinkedMap<String, Integer> reMap = new LinkedMap<String, Integer>();
        for (int i = 0; i < size && i < getCount; i++) {
            Entry<String, Integer> entery = list.get(i);
            String key = entery.getKey();
            if (key.matches("[0-9]+")) {
                key = key + " ";
            }
            reMap.put(key, entery.getValue());
        }
        return reMap;
    }

    /**
     * 字符串拼接，
     * str 原字符串
     * appendStr 拼接字符
     * split 拼接符号
     * hasHrackets 是否需要括号只有一个就不加括号
     **/
    public static String appendString(List<String> str, String split, boolean hasHrackets) {
        StringBuffer buf = new StringBuffer();

        if (hasHrackets && str != null && str.size() > 1) {
            for (int i = 0; i < str.size(); i++) {
                String tempStr = str.get(i);
                if (!StringUtils.isEmpty(tempStr)) {
                    buf.append("(").append(tempStr).append(")").append(split);
                }

            }
        } else {
            for (int i = 0; i < str.size(); i++) {
                String tempStr = str.get(i);
                if (!StringUtils.isEmpty(tempStr)) {
                    buf.append(tempStr).append(split);
                }

            }
        }
        String resultStr = buf.toString();
        if (resultStr.endsWith(split)) {
            resultStr = resultStr.substring(0, resultStr.length() - 1);
        }

        return resultStr;
    }

    public static boolean isNumber(String str) {
        boolean flag = false;

        flag = PATTERN_2.matcher(str).matches();
        return flag;
    }

    /**
     * 11位的手机号脱敏
     **/
    public static String desensitizeDelectronicsAccountNo(String phoneNo) {
        if (isNumber(phoneNo)) {
            if (phoneNo.length() == 11) {
                phoneNo = phoneNo.replaceAll("(\\w{3})\\w*(\\w{4})", "$1*****$2");
            }
        }
        return phoneNo;
    }

    /**
     * list integer 转 String
     **/
    public static String listToString(List<Integer> list, String split) {
        String tagString = "";
        split = isEmpty(split) ? "," : split;
        //组织的标签处理
        if (list != null && list.size() > 0) {
            tagString = list.stream().map(String::valueOf).collect(Collectors.joining(split));
        }
        return tagString;

    }


    /**
     * list integer 转 String
     **/
    public static List<Integer> stringToList(String str, String split) {
        split = isEmpty(split) ? "," : split;
        List<Integer> result = new ArrayList<Integer>();

        if (isNotEmpty(str)) {
            String[] strArray = str.split(split);
            for (int i = 0; i < strArray.length; i++) {
                String tempStr = strArray[i];
                if (isNotEmpty(tempStr)) {
                    result.add(Integer.valueOf(tempStr));
                }
            }
        }

        return result;

    }


    /**
     * list String 转 String
     **/
    public static List<String> stringToListStr(String str, String split) {
        split = isEmpty(split) ? "," : split;
        List<String> result = new ArrayList<String>();

        if (isNotEmpty(str)) {
            String[] strArray = str.split(split);
            for (int i = 0; i < strArray.length; i++) {
                String tempStr = strArray[i];
                if (isNotEmpty(tempStr)) {
                    result.add(tempStr);
                }
            }
        }

        return result;

    }

    public static Integer getMediaKey(Map<Integer, String> map, String name) {
        if (map == null || isEmpty(name)) {
            return null;
        }

        for (Integer key : map.keySet()) {
            if (name.equals(map.get(key))) {
                return key;
            }

        }
        return null;
    }


    /**
     * 统计出现次数
     **/
    public static int textKeyWordCount(String keywords, String text) {
        int count = 0;
        if (isEmpty(keywords) || isEmpty(text)) {
            return count;
        }
        while (text.indexOf(keywords) != -1) {
            count++;
            text = text.substring(text.indexOf(keywords)
                    + keywords.length());

        }
        return count;
    }

    public static boolean checkSourceName(String name) {
        String regex = "^([a-z0-9A-Z\u4e00-\u9fa5|,|，|。|.|—|-|-|(|)|（|）])+$";
        return name.matches(regex);
    }


    /**
     * 判断是不是微信微博的连接
     **/
    public static Integer getUrlType(String url) {
        //https://mp.weixin.qq.com/s/O-7znhhYt5DHDxcOq1Ga5Q
        if (url.contains("weixin.qq")) {
            return 2;
        } else if (url.contains("m.weibo.cn") || url.contains("www.weibo.com") || url.contains("weibo.com")) {
            return 0;
        } else if (url.contains("www.toutiao.com") || url.contains("toutiaoba.com") || url.contains("mp.toutiao.com")) {
            return 1;
        }

        return -1;
    }

    public static String combinationIds(String oldIds, String joinId) {
        if (StringUtils.isBlank(oldIds)) {
            oldIds = "";
        }

        if (StringUtils.isBlank(joinId)) {
            joinId = "";
        }

        List<String> idList = Arrays.asList(oldIds.split(","));
        idList = idList.stream().filter(item -> StringUtils.isNoneBlank(item)).collect(Collectors.toList());
        List<String> newIdList = Arrays.asList(joinId.split(","));
        newIdList = newIdList.stream().filter(item -> StringUtils.isNoneBlank(item)).collect(Collectors.toList());

        for (String id : newIdList) {
            if (StringUtils.isNoneBlank(id) && !idList.contains(id)) {
                idList.add(id);
            }
        }

        String newIds = idList.stream().collect(Collectors.joining(","));
        if (StringUtils.isNoneBlank(newIds)) {
            newIds = String.format(",%s,", newIds);
        }
        return newIds;
    }

    public static void main(String[] args) {
        System.out.println(isHttpUrl("https://pub-zhtb.hizh.cn/a/202304/25/AP6447813fe4b0fe4f8204b6fd.html"));
    }
}
