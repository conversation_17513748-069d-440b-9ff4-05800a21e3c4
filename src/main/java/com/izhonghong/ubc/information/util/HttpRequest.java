package com.izhonghong.ubc.information.util;

import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.OkHttpClient.Builder;
import org.apache.http.util.TextUtils;


/**
 * 功能描述: 基于okhttp3的http请求工具类
 * <AUTHOR>
 * @date 2019-06-13 16:46:42
 */
@Slf4j
public final class HttpRequest {
    private static OkHttpClient client = createClient();
    private Request.Builder builder = new Request.Builder();
    private String url;

    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    public static final MediaType FORM = MediaType.parse("application/x-www-form-urlencoded; charset=utf-8");

    private static String[] VERIFY_HOST_NAME_ARRAY = new String[]{};

    private static void resetClient() {
        client.connectionPool().evictAll();
        client = createClient();
    }

    private static OkHttpClient createClient() {
    	Builder builder = new okhttp3.OkHttpClient.Builder();
    	builder.readTimeout(120, TimeUnit.SECONDS);
    	builder.connectTimeout(20, TimeUnit.SECONDS);
    	
        OkHttpClient okHttpClient = (builder).hostnameVerifier(createInsecureHostnameVerifier()).build();
        return okHttpClient;
    }

    public static HostnameVerifier createInsecureHostnameVerifier() {
        return new HostnameVerifier() {
            @Override
            public boolean verify(String hostname, SSLSession session) {
                if (TextUtils.isEmpty(hostname)) {
                    return false;
                }
                return !Arrays.asList(VERIFY_HOST_NAME_ARRAY).contains(hostname);
            }
        };
    }


    public HttpRequest(String url) {
        this.url = url;
        this.builder.url(url);
    }

    public HttpRequest addHeader(String name, String value) {
        this.builder.addHeader(name, value);
        return this;
    }

    public HttpRequest addHeader(String name, boolean value) {
        this.builder.addHeader(name, Boolean.toString(value));
        return this;
    }

    public HttpRequest get() {
        this.builder.get();
        return this;
    }

    public HttpRequest get(Map<String, Object> params) {
        if (params == null) {
            return get();
        }
        StringBuilder s = new StringBuilder();
        Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            s.append("&").append(entry.getKey()).append("=").append(Optional.ofNullable(entry.getValue()).orElse(""));
        }
        String url = s.toString().replaceFirst("&", "?");
        this.url +=  url;
        this.builder.url(this.url).get();
        return this;
    }

    public HttpRequest post(RequestBody requestBody) {
        this.builder.post(requestBody).build();
        return this;
    }

    public HttpRequest postJson(String json) {
    	log.info("【" + this.url + "】参数：" + json);
        this.builder.post(RequestBody.create(JSON, json)).build();
        return this;
    }
    
    public HttpRequest postJson(JSONObject json) {
        return postJson(json.toJSONString());
    }

    public HttpRequest postFormEncoded(Map<String, Object> params) {
        FormBody.Builder builder = new FormBody.Builder();
        Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            builder.addEncoded(entry.getKey(), entry.getValue() + "");
        }
        this.builder.post(builder.build()).build();
        return this;
    }

    public Response execute() throws IOException {
        okhttp3.Request request = this.builder.build();
        try {
            return client.newCall(request).execute();
        } catch (IOException var5) {
            resetClient();
            return client.newCall(request).execute();
        }
    }

    public String executeAsString() throws IOException {
        Response execute = execute();
        return execute.body().string();
    }

    public JSONObject executeAsJsonObject() throws IOException {
    	long startMs = System.currentTimeMillis();
        JSONObject resultJson = JSONObject.parseObject(Optional.ofNullable(executeAsString()).orElse("{}"));
    	if (this.url.indexOf("api/v1/search") == -1) { //列表接口不输出接口
    		log.info("【" + this.url + "】，耗时:" + (System.currentTimeMillis() - startMs) + "，结果：" + resultJson);
    	}
        return resultJson;
    }
}
