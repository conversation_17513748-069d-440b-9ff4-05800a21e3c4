package com.izhonghong.ubc.information.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Component;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.afterturn.easypoi.word.entity.params.ExcelListEntity;
import cn.afterturn.easypoi.word.parse.excel.ExcelEntityParse;
import freemarker.core.ParseException;
import freemarker.template.Configuration;
import freemarker.template.MalformedTemplateNameException;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import freemarker.template.TemplateNotFoundException;
import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

@Component
@Slf4j
public class ExportWordUtils {

	private Configuration config = null;
	
	public ExportWordUtils() {
		 config = new Configuration(Configuration.VERSION_2_3_28);
	     config.setDefaultEncoding("utf-8");
	}
	
	 /**
     * FreeMarker生成Word
     * @param dataMap 数据
     * @param templateName 目标名
     * @param saveFilePath 保存文件路径的全路径名（路径+文件名）
     */
    public void createWord(Map<String, Object> dataMap, String templateName, String saveFilePath,String  servletContextPath) {
    	  //加载模板(路径)数据
    	 //指定路径的第二种方式，我的路径是C：/a.ftl
    	 Template template = null;
    	 File file = null;
    	 try {
    		 file = new File(servletContextPath);
	    	config.setDirectoryForTemplateLoading(file);

	        //设置异常处理器 这样的话 即使没有属性也不会出错 如：${list.name}...不会报错
	        config.setTemplateExceptionHandler(TemplateExceptionHandler.IGNORE_HANDLER);
	       
	        //以utf-8的编码读取ftl文件
            template = config.getTemplate(templateName, "utf-8");
        } catch (TemplateNotFoundException e) {
            log.error("模板文件未找到", e);
            e.printStackTrace();
        } catch (MalformedTemplateNameException e) {
            log.error("模板类型不正确", e);
            e.printStackTrace();
        } catch (ParseException e) {
            log.error("解析模板出错，请检查模板格式", e);
            e.printStackTrace();
        } catch (IOException e) {
            log.error("IO读取失败", e);
            e.printStackTrace();
        }
    	 
        File outFile = new File(saveFilePath);
        if(!outFile.getParentFile().exists()) {
            outFile.getParentFile().mkdirs();
        }
        
        Writer out = null;
        FileOutputStream fos = null;
        try {
	        try {
	            fos = new FileOutputStream(outFile);
	        } catch (FileNotFoundException e) {
	            log.error("输出文件时未找到文件", e);
	            e.printStackTrace();
	        }
	        
	         out = new OutputStreamWriter(fos, "UTF-8");
	        //将模板中的预先的代码替换为数据
	        try {
	            template.process(dataMap, out);
	        } catch (TemplateException e) {
	            log.error("填充模板时异常", e);
	            e.printStackTrace();
	        } catch (IOException e) {
	            log.error("IO读取时异常", e);
	            e.printStackTrace();
	        }
	        log.info("由模板文件：" + templateName + ".ftl" + " 生成文件 ：" + saveFilePath + " 成功！！");
        }catch (Exception e) {
			e.printStackTrace();
			log.error("由模板导出文件报错",e);
		}finally {
			  try {
				  
				  if(out != null) {
					  out.flush();
					  out.close();
				  }
		        } catch (IOException e) {
		            log.error("关闭Write对象出错", e);
		            e.printStackTrace();
		        }
			  
				try {
					 if(fos != null) {
						  fos.flush();
						  fos.close();
					  }
				} catch (IOException e) {
					log.error("fos关闭异常",e);
					e.printStackTrace();
				}
				
		}
       
    }
    

	/**
     * 获得图片的Base64编码
     * @param imgFile
     * @return
     */
    public String getImageStr(String imgFile) {
        InputStream in = null;
        byte[] data = null;
        try {
	      
	        try {
	            in = new FileInputStream(imgFile);
	        } catch (FileNotFoundException e) {
	            log.error("加载图片未找到", e);
	            e.printStackTrace();
	        }
	        try {
	            data = new byte[in.available()];
	            //注：FileInputStream.available()方法可以从输入流中阻断由下一个方法调用这个输入流中读取的剩余字节数
	            in.read(data);
	        } catch (IOException e) {
	            log.error("IO操作图片错误", e);
	            e.printStackTrace();
	        }
        }catch (Exception e) {
        	  log.error("操作图片错误", e);
	          e.printStackTrace();
		}finally {
			 try {
				 if(in != null) {
				     in.close();
				 }
			} catch (IOException e) {
				log.error("InputStream 关闭异常", e);
				e.printStackTrace();
			}
			
		}
        
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
        
    }
    
    /**
     * 根据参数与文件路径生成word文件
     * @param param
     * @param wordPath
     */
    public static void saveWord(Map<String,Object> param,List<?> list, String saveFilePath,String  templPath,Class<?> clazz) {
         log.info("=============word 模板开始生成  =======");
    	//调用的模板
    	XWPFDocument doc = null;
    	FileOutputStream fos = null;
        try {
             doc = WordExportUtil.exportWord07(templPath, param);
             fos = new FileOutputStream(saveFilePath);
            if(!StringUtil.isListNull(list)) {
            	new ExcelEntityParse().parseNextRowAndAddRow(doc.getTableArray(1), 1, new ExcelListEntity(list, clazz, 1));
            }
            doc.write(fos);
            
        } catch (Exception e) {
        	log.error("saveWord 生成Word文档异常{}", e);
            e.printStackTrace();
        }finally {
        	if (null != fos){
				try {
					fos.flush();
					fos.close();
				} catch (IOException e) {
					log.error("FileOutputStream 关闭异常{}", e);
					e.printStackTrace();
				}
			}
        	if (null != doc){
				try {
					doc.close();
				} catch (IOException e) {
					log.error("XWPFDocument 关闭异常{}", e);
					e.printStackTrace();
				}
			}
        }
        log.info("=============word 模板开始结束  =======");
    }

}
