package com.izhonghong.ubc.information.util;

import com.izhonghong.ubc.inf.core.constant.ErrorCodeEnum;
import com.izhonghong.ubc.inf.core.protocol.IzhResponseBody;
import com.izhonghong.ubc.information.constant.ReturnEnum;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Collection;

/**
 * 功能描述: 常用返回体封装
 * <AUTHOR> 
 * @date 2019-06-04 11:07:52
 */
public class ResponseUtil {

    public static IzhResponseBody<Object> responose(boolean success) {
        return success ? ok() : error();
    }

    public static IzhResponseBody<Object> responoseByFlag(boolean flag) {
        return flag ? ok() : error(ReturnEnum.FAIL.code,ReturnEnum.FAIL.msg);
    }

    public static IzhResponseBody<Object> ok() {
        return new IzhResponseBody<>(ErrorCodeEnum.OK.getErrorCode(), ErrorCodeEnum.OK.getErrorMessage(), null);
    }

    public static IzhResponseBody<Object> ok(Object data) {
        return new IzhResponseBody<>(ErrorCodeEnum.OK.getErrorCode(), ErrorCodeEnum.OK.getErrorMessage(), data);
    }

    public static IzhResponseBody<Object> okIfNotEmpty(Object data) {
        if (data == null) {
            return error(ErrorCodeEnum.NOT_FOUND);
        }
        if (data instanceof Collection) {
            if (((Collection) data).isEmpty()) {
                return error(ErrorCodeEnum.NOT_FOUND);
            }
        }
        return ok(data);
    }

    public static IzhResponseBody<Object> error() {
        return new IzhResponseBody<>(ErrorCodeEnum.UNHANDLED_EXCEPTION.getErrorCode(), ErrorCodeEnum.UNHANDLED_EXCEPTION.getErrorMessage(), null);
    }

    public static IzhResponseBody<Object> error(String msg) {
        return new IzhResponseBody<>("500", msg, null);
    }

    public static IzhResponseBody<Object> error(ErrorCodeEnum errorCodeEnum) {
        return new IzhResponseBody<>(errorCodeEnum.getErrorCode(), errorCodeEnum.getErrorMessage(), null);
    }

    public static IzhResponseBody<Object> error(String code, String msg) {
        return new IzhResponseBody<>(code, msg, null);
    }

    public static IzhResponseBody error(BindingResult bindingResult) {
        String msg = bindingResult.getAllErrors().stream()
                .map(err -> err.getDefaultMessage())
                .reduce((a, b) -> a + ";" + b)
                .orElse(ErrorCodeEnum.DATA_INVALID.getErrorMessage());
        return error(ErrorCodeEnum.DATA_INVALID.getErrorCode(), msg);
    }

    public static IzhResponseBody<Object> error(ReturnEnum returnEnum) {
        return new IzhResponseBody<>(returnEnum.code, returnEnum.msg, null);
    }

	public static void setFileHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setHeader("Content-Disposition","attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
    }
}
