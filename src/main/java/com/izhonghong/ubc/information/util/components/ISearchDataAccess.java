package com.izhonghong.ubc.information.util.components;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.izhonghong.ubc.information.util.HttpRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽离出isearch数据访问层
 * **/
@Slf4j
public class ISearchDataAccess {
	
	public static JSONObject postJson(String url, JSONObject paramBody) throws Exception {
		
		JSONObject resultJson = new HttpRequest(url).postJson(paramBody)
		        .executeAsJsonObject();
		
		if(resultJson == null ) {
			throw new Exception("ISearch 数据返回为空");
		}
		
		// 添加类型检查，避免ClassCastException
		if (resultJson.containsKey("data")) {
			Object data = resultJson.get("data");
			if (data instanceof JSONObject) {
				return (JSONObject) data;
			} else if (data instanceof JSONArray) {
				// 如果data是JSONArray，创建一个新的JSONObject并把整个结果放进去
				return resultJson;
			} else {
				// 其他情况，返回原始结果
				return resultJson;
			}
		}
		
		return resultJson;
		
	}
	
  public static JSONObject postAndHasData(String url,JSONObject paramBody) throws Exception {
		
		JSONObject resultJson = new HttpRequest(url).postJson(paramBody)
		        .executeAsJsonObject();
		
		if(resultJson == null ) {
			throw new Exception("ISearch 数据返回为空");
		}
		return resultJson;
		
	}
  
  public static JSONObject postAndHasData(String url,String paramBody) throws Exception {
		
		JSONObject resultJson = new HttpRequest(url).postJson(paramBody)
		        .executeAsJsonObject();
		
		if(resultJson == null ) {
			throw new Exception("ISearch 数据返回为空");
		}
		return resultJson;
		
	}
  
  public static JSONObject postFormEncoded(String url,JSONObject paramBody) throws Exception {
		
		JSONObject resultJson = new HttpRequest(url).postFormEncoded(paramBody)
		        .executeAsJsonObject();
		
		if(resultJson == null ) {
			throw new Exception("ISearch 数据返回为空");
		}
		return resultJson;
		
	}
	public static JSONObject getAndHasData(String url) throws Exception {

		JSONObject resultJson = new HttpRequest(url).get()
				.executeAsJsonObject();

		if(resultJson == null ) {
			throw new Exception("ISearch 数据返回为空");
		}
		return resultJson;

	}
	public static String getHttpRequest(String url) throws Exception {

		String resultJson = new HttpRequest(url).get().executeAsString();

		if(resultJson == null ) {
			throw new Exception("ISearch 数据返回为空");
		}
		return resultJson;

	}

	
	
}
