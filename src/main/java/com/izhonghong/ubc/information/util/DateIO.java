package com.izhonghong.ubc.information.util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateIO {
//	public static void main(String[] args) {
//		Date date = new DateIO().strToDate("2013-04-01");
//		String strdate = new DateIO().dateToStr(date);
//		String srrdate = new DateIO()
//				.timestampToStr(System.currentTimeMillis());
//		Timestamp ts = new DateIO().strToTimestamp(new Date());
//	}

	public static Date strToDate1(String strdate) {
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = null;
		try {
			date = format.parse(strdate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
//		System.out.println("date:" + date);
		return date;
	}
	
	// String 转换为 Date
	public static Date strToDate(String strdate) {
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Date date = null;
		try {
			date = format.parse(strdate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
//		System.out.println("date:" + date);
		return date;
	}

	// Date 转换为 String
	public static String dateToStr(Date date) {

		// 年月日****-**-**
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		String str = format.format(date);
		//System.out.println("str:" + str);

		// 年月日**-*-*
		format = DateFormat.getDateInstance(DateFormat.SHORT);
		str = format.format(date);
		//System.out.println(str);

		// 年月日****-*-*
		format = DateFormat.getDateInstance(DateFormat.MEDIUM);
		str = format.format(date);
		//System.out.println(str);

		// ****年*月*日星期*
		format = DateFormat.getDateInstance(DateFormat.FULL);
		str = format.format(date);
//		System.out.println(str);
		return str;
	}

	// Date 转换为 String
	public static String dateToStr2(Date date) {

		// 年月日****-**-**
		DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		String str = format.format(date);
		//System.out.println("str:" + str);

		// 年月日**-*-*
		format = DateFormat.getDateInstance(DateFormat.SHORT);
		str = format.format(date);
		//System.out.println(str);

		// 年月日****-*-*
		format = DateFormat.getDateInstance(DateFormat.MEDIUM);
		str = format.format(date);
		//System.out.println(str);
 
		return str;
	}
	
	// Timestamp转换为String
	public String timestampToStr(Long timestamp) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 定义格式，不显示毫秒
		String str = df.format(timestamp);
		System.out.println(str);
		return str;
	}

	// Date转换为Timestamp
	public Timestamp strToTimestamp(Date date) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String time = df.format(date);
		Timestamp ts = Timestamp.valueOf(time);
		System.out.println(ts);
		return ts;
	}
	// 获取当前时间
	public static String getNowTime(){
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
		return df.format(new Date());
	}
	
	// 获取当前时间
	public static Date getNowDateTime(){
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
		return new Date();
	}
	public static String getNowTime1(){
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
		return df.format(new Date());
	}
	
	public static String getNowTime2(){
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
		return df.format(new Date());
	}
	
	public static String beforeOneMonth(int month){
	  Date date = new Date();//当前日期
	  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化对象
	  Calendar calendar = Calendar.getInstance();//日历对象
	  calendar.setTime(date);//设置当前日期
	  calendar.add(Calendar.MONTH, -month);//月份减一
	  System.out.println(sdf.format(calendar.getTime()));//输出格式化的日期
	  return sdf.format(calendar.getTime());
	}
	public static String beforeOneWeek(int day){
		SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
		Date beginDate = new Date();
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.set(Calendar.DATE, date.get(Calendar.DATE) - day);
		return dft.format(date.getTime());
	}
	
	public static String beforeSomeMinute (int minute){
		Date date = new Date(System.currentTimeMillis()-minute*60*1000);
		SimpleDateFormat myFmt=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return myFmt.format(date);
	}
	
	public static List getDateList(int length){
		List list = new ArrayList();
		for(int i=1;i<length;i++){
			list.add(beforeOneWeek(7-i));
		}
		list.add(getNowTime1());
		return list;
	}
	
	
	public static int compareDate(String date1, String date2) {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm");
		try {
			Date dt1 = df.parse(date1);
			Date dt2 = df.parse(date2);
			if (dt1.getTime() > dt2.getTime()) {
//				System.out.println("dt1 在dt2前");
				return 1;
			} else if (dt1.getTime() < dt2.getTime()) {
//				System.out.println("dt1在dt2后");
				return -1;
			} else {
				return 0;
			}
		} catch (Exception exception) {
			exception.printStackTrace();
		}
		return 0;
	}
	
	public static String beforeSecond(int second){
		SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date beginDate = new Date();
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.set(Calendar.SECOND, date.get(Calendar.SECOND) - second);
		return dft.format(date.getTime());
	}
}