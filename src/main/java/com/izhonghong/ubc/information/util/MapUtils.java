package com.izhonghong.ubc.information.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class MapUtils {


    /***
     * list<map>排序
     *    入参：数据，排序规则desc倒叙，空或des正叙，排序字段key
     */
    public static List<Map<String, Object>> listMapSort(List<Map<String, Object>> list, final String orderd, final String key) {
        List<Map<String, Object>> listmap = new ArrayList<Map<String, Object>>();
        Collections.sort(list, new Comparator<Map<String, Object>>() {
            public int compare(Map o1, Map o2) {
                if ("desc".equals(orderd)) {
                    return (o2.get(key).toString()).compareTo(o1.get(key).toString());
                } else if ("des".equals(orderd) || "".equals(orderd)) {
                    return (o1.get(key).toString()).compareTo(o2.get(key).toString());
                } else {
                    return 0;
                }

            }
        });
        return list;
    }

}
