package com.izhonghong.ubc.information;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.servlet.MultipartConfigElement;
import java.io.File;

@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@ComponentScan(basePackages = {"com.izhonghong.ubc.*"})
@MapperScan("com.izhonghong.ubc.information.dao")
@EnableAsync
@EnableDiscoveryClient
@EnableScheduling
public class InformationApplication extends SpringBootServletInitializer {
	
	@Bean
    public MultipartConfigElement configElement() {
		
        MultipartConfigFactory multipartConfigFactory = new MultipartConfigFactory();
        String location = System.getProperty("user.dir") + "/uploadFile/tmp";
        File file = new File(location);
        if(!file.exists()){
           file.mkdirs();
        }
        multipartConfigFactory.setLocation(location);
        return multipartConfigFactory.createMultipartConfig();
    }

	public static void main(String[] args) {
		SpringApplication.run(InformationApplication.class, args);
	}

}
