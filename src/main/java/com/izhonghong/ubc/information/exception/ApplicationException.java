package com.izhonghong.ubc.information.exception;

import com.izhonghong.ubc.information.result.StatusCode;

/**
 * 自定义系统异常
 */
public class ApplicationException extends RuntimeException{

	private static final long serialVersionUID = -7883840067913852752L;

	private String code;

	public ApplicationException(String code, String message) {
		super(message);
		this.code = code;
	}

	public ApplicationException(StatusCode statusCode){
		super(statusCode.message());
		this.code = statusCode.code();
	}
	public ApplicationException(StatusCode statusCode, String message) {
		super(message);
		this.code = statusCode.code();
	}

	public ApplicationException(String message) {
		super(message);
		this.code = StatusCode.SYS_COMMON_SERVER_UNKNOWN_ERROR.code();
	}

	public String getCode() {
		return code;
	}
}
