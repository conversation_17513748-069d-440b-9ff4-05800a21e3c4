package com.izhonghong.ubc.information.exception;

import com.izhonghong.ubc.information.common.annotation.OAuth;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.ui.ModelMap;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理类
 */
@ControllerAdvice
@RequestMapping("/error")
@Slf4j
public class GlobalExceptionHandler extends BasicErrorController {

	@Autowired
	private ErrorAttributes errorAttributes;

	@Autowired
	public GlobalExceptionHandler(ErrorAttributes errorAttributes) {
		super(errorAttributes, new ErrorProperties());
		this.errorAttributes = errorAttributes;
	}

	@OAuth(required = false)
	@RequestMapping(Result.NOT_OAUTH)
	public Result unauthorized(){
		return Result.unauthorized();
	}

	@OAuth(required = false)
	@RequestMapping(Result.PERMISSION_DENIED)
	public Result permissionDenied(){
		return Result.permissionDenied();
	}

	@OAuth(required = false)
	@RequestMapping(Result.SUPER_PERMISSION_DENIED)
	public Result superPermissionDenied(){
		return Result.superPermissionDenied();
	}

	@OAuth(required = false)
	@RequestMapping(Result.INVALID_PARAM)
	public Result invalidParam(ModelMap modelMap){
		return new Result(Result.INVALID_PARAM, modelMap.get("message").toString());
	}


	/**
	 * 系统全局异常处理
	 * @param request
	 * @return
	 */
	@OAuth(required = false)
	@GetMapping(name = "错误页", value = {"/500", ""})
	public Result err(HttpServletRequest request) {
		HttpStatus status = getStatus(request);
		Result result = Result.systemError();
		if(HttpStatus.NOT_FOUND.equals(status)) {
			result = Result.notFound();
		}
//		RequestAttributes requestAttributes = new ServletRequestAttributes(request);
		WebRequest webRequest = new ServletWebRequest(request);
		Throwable throwable = this.errorAttributes.getError(webRequest);
		if(throwable == null){
			return result;
		}
		if(throwable instanceof ApplicationException){
			ApplicationException ex = (ApplicationException) throwable;
			result.setCode(ex.getCode());
			result.setMessage(ex.getMessage());
			log.info("{} at {}", ex);
		}else {
			log.error("未知异常：" + ExceptionUtils.getStackTrace(throwable));
		}
		return result;
	}


	/**
	 * Service层全局异常处理
	 * @param e
	 * @return
	 */
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(Exception.class)
	public Result handleException(Exception e) {
		if(e instanceof ApplicationException){
			log.info("{} at {}", e.getMessage(), e.getStackTrace()[0].toString());
			ApplicationException ex = (ApplicationException) e;
			return new Result(ex.getCode(),ex.getMessage());
		}
		if(e instanceof HttpRequestMethodNotSupportedException){
			return new Result(StatusCode.SYS_COMMON_SERVICE_BUSY, e.getMessage());
		}
		if(e instanceof DuplicateKeyException){
			return new Result(StatusCode.SYS_COMMON_OPERATION_FAIL, "数据重复");
		}
		log.error(ExceptionUtils.getStackTrace(e), e);
		return Result.systemError();
	}

}
