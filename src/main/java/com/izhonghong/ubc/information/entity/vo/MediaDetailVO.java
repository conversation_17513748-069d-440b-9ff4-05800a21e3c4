package com.izhonghong.ubc.information.entity.vo;

import java.io.Serializable;
import java.util.List;

import com.izhonghong.ubc.information.util.TreeUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value="信息源详情")
@Data
public class MediaDetailVO implements Serializable {

        @ApiModelProperty(name = "区域")
        private String area;

        @ApiModelProperty(name = "编码")
        private String code;

        @ApiModelProperty(name = "媒体等级")
        private String media_level;

        @ApiModelProperty(name = "创建时间")
        private Long create_time;

        @ApiModelProperty(name = "站点域名")
        private String site_domain;

        @ApiModelProperty(name = "新闻版块")
        private String news_section;

        @ApiModelProperty(name = "行业")
        private String industry;

        @ApiModelProperty(name = "ES索引名称")
        private String es_index_name;

        @ApiModelProperty(name = "K3系统ID")
        private Integer k3_id;

        @ApiModelProperty(name = "媒体信息标识")
        private Integer media_info_tag;

        @ApiModelProperty(name = "更新时间")
        private Long update_time;

        @ApiModelProperty(name = "网站链接")
        private String web_url;

        @ApiModelProperty(name = "媒体类型")
        private String media_type;

        @ApiModelProperty(name = "主办单位")
        private String organizer;

        @ApiModelProperty(name = "名称")
        private String name;

        @ApiModelProperty(name = "ID")
        private Integer id;

        @ApiModelProperty(name = "新编码")
        private String new_code;

        @ApiModelProperty(name = "备注")
        private String remarks;

        @ApiModelProperty(name = "状态")
        private Integer status;

        @ApiModelProperty(name = "新名称")
        private String new_name;

    }




