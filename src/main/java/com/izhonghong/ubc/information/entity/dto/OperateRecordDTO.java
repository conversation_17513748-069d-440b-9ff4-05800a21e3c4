package com.izhonghong.ubc.information.entity.dto;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class OperateRecordDTO {
	
	/**
	 * 页码
	 * **/
	private int pageNo=1;
	
	/**
	 * 每页条数
	 * */
	private int pageSize=50;
	/**
	 * 起始时间
	 * **/
	public String startTime;
	
	/***
	 * 结束时间
	 * **/
	public String endTime;
	
	/**
	 * IP地址
	 * **/
	public String addressIp;
	
	 /**
     * 主键id
     **/
    private Integer id;

    /**
     * 操作模块
     **/
    private String operateModule;

    /**
     * 操作者id
     **/
    private Integer operatorId;

    /**
     * 操作者所属组织
     **/
    private Integer operatorOrgId;

    /**
     * 操作者名字(登录名)
     **/
    private String operatorName;

    /**
     * 操作者的用户类型
     **/
    private Integer operatorUsertype;

    /**
     * 操作类型,1删除/2修改
     */
    private Integer operateType;

    /**
     * 操作时间
     **/
    private LocalDateTime operateTime;

    /**
     * 操作结果,1成功/0失败
     **/
    private Boolean operateResult;

    /**
     * 如果操作失败,返回原因
     **/
    private String reason;

    /**
     * 操作所属ip
     **/
    private String ip;

    private String description;

    private String usedTime;

}
