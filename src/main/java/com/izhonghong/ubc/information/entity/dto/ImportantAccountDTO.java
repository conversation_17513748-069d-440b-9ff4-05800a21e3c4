package com.izhonghong.ubc.information.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date 2023-07-05 14:38:11
 */
@Data
public class ImportantAccountDTO {

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 账号名称
     */
    private String name;

    /**
     * 账号UID
     */
    private String uid;

    /**
     * 所属组织ID
     */
    private String orgIds;

    /**
     * 所属行业ID
     */
    private String industryIds;

    /**
     * 所属区域ID
     */
    private String areaIds;

    /**
     * 主页链接
     */
    private String homePage;

}
