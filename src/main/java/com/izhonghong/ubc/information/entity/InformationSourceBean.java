package com.izhonghong.ubc.information.entity;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 信息源实体
 *
 * <AUTHOR>
 **/
@Data
public class InformationSourceBean {

    /**
     * 信息源名称
     **/
    private String name;

    @ApiModelProperty(value = "媒体主页")
    private String web_url;

    /**
     * 信息id
     **/
    private String id;


    /**
     * 信息源编号
     **/
    private String code;

    /**
     * 信息源编号
     **/
    private List<String> codeList;

    /***
     * 微信的id
     * **/
    private String uid;

    /**
     * @see SourceTypeMenu
     * 用户类型
     **/
    private String sourceType;

    /**
     * 媒体所属省份
     **/
    private String area;

    private String organizer;

    /**
     * 媒体所属城市
     **/
    private String city;

    /**
     * 媒体类型
     **/
    private String mediaType;

    @ApiModelProperty(value = "媒体性质")
    private String media;

    @ApiModelProperty(value = "媒体类型：微博微信网站等一级分类")
    private String media_type;

    @ApiModelProperty(value = "排序字段")
    private String sortField;

    @ApiModelProperty(value = "升降序")
    private String sort;

    /**
     * 媒体类型
     **/
    private String key;


    private String serviceUrl;


    private Integer status;

    private Integer pageNo;

    private Integer size;

    private String mediaLevel;

    private String industry;

    private String mediaInfo;


    public void setInformationService(String service, String url) {
        this.serviceUrl = service + url;
    }

    public JSONObject paramBody() {
        JSONObject paramBody = new JSONObject();
        paramBody.put("name", this.name);
        if (!StringUtils.isEmpty(this.web_url)) {
            paramBody.put("web_url", this.web_url);
        }

        if (!StringUtils.isEmpty(this.media_type)) {
            paramBody.put("media_type", this.media_type);
        }

        if (!StringUtils.isEmpty(this.area)) {
            paramBody.put("area", this.area);
        }

        if (!StringUtils.isEmpty(this.code)) {
            paramBody.put("code", this.code);
        }

        if (!StringUtils.isEmpty(this.organizer)) {
            paramBody.put("organizer", this.organizer);
        }

        if (!StringUtils.isEmpty(this.media)) {
            paramBody.put("media", this.media);
        }

        if (!StringUtils.isEmpty(this.status)) {
            paramBody.put("status", this.status);
        }

        if (this.pageNo != null) {
            paramBody.put("page", this.pageNo);
        }

        if(!StringUtils.isEmpty(this.mediaLevel)){
            paramBody.put("mediaLevel", this.mediaLevel);
        }

        if(!StringUtils.isEmpty(this.industry)){
            paramBody.put("industry", this.industry);
        }

        if(!StringUtils.isEmpty(this.mediaInfo)) {
            paramBody.put("mediaInfo", this.mediaInfo);
        }

        if (!StringUtils.isEmpty(this.sortField)) {
            paramBody.put("sortField", this.sortField);
        } else {
            paramBody.put("sortField", "updateTime");
        }

        if (!StringUtils.isEmpty(this.sort)) {
            paramBody.put("sortValue", this.sort);
        }


        if (this.size != null) {
            paramBody.put("size", this.size);
        }

        if (this.codeList != null && this.codeList.size() > 0) {
            paramBody.put("codeList", this.codeList);
        }

        return paramBody;
    }


    public String cleanArea(String area) {
        if (StringUtils.isEmpty(area)) {
            return area;
        }
        area = area.replace("境内", "").replace("境外", "");
        if (area.startsWith(".")) {
            area = area.substring(1, area.length());
        }
        String[] areaArray = area.split("\\.");
        for (int i = 0; i < areaArray.length; i++) {
            String temp = areaArray[i];
            if (temp.endsWith("市")) {
                temp = temp.substring(0, temp.length() - 1);
            } else if (temp.endsWith("省")) {
                temp = temp.substring(0, temp.length() - 1);
            } else if (temp.startsWith("广西")) {
                temp = "广西";
            } else if (temp.startsWith("内蒙古")) {
                temp = "内蒙古";
            } else if (temp.startsWith("新疆")) {
                temp = "新疆";
            } else if (temp.startsWith("西藏")) {
                temp = "西藏";
            } else if (temp.startsWith("宁夏")) {
                temp = "宁夏";
            } else if (temp.endsWith("县")) {
                temp = temp.substring(0, temp.length() - 1);
            } else if (temp.endsWith("区")) {
                temp = temp.substring(0, temp.length() - 1);
            }
            areaArray[i] = temp;
        }
        area = String.join(".", areaArray);
        return area;
    }
}
