package com.izhonghong.ubc.information.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *      媒体库
 * **/
@Data
public class MediaLibrary {
	/**
	 * 信息id
	 **/
	@ApiModelProperty(name = "主键id")
	private String id;

	/**
	 * 信息源编号
	 **/
	@ApiModelProperty(name = "信息源编号")
	private Integer code;

	@ApiModelProperty(name = "主页url")
	private String web_url;

	@ApiModelProperty(name = "媒体类型")
	private String media_type;

	@ApiModelProperty(name = "媒体性质")
	private String media;

	@ApiModelProperty(name = "媒体信息")
	private String mediaInfo;

	@ApiModelProperty(name = "媒体等级")
	private String mediaLevel;

	@ApiModelProperty(name = "所属行业")
	private String industry;
}
