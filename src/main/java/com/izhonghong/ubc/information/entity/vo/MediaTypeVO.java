package com.izhonghong.ubc.information.entity.vo;

import java.io.Serializable;
import java.util.List;

import com.izhonghong.ubc.information.util.TreeUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/***
 * <AUTHOR>
 * **/
@ApiModel(value="媒体类型分类")
@Data
public class MediaTypeVO implements TreeUtil.TreeAble<MediaTypeVO>,Serializable{
	
	@ApiModelProperty(name="id")
	private Integer id;
	
	@ApiModelProperty(name="名称")
	private String name;
	
	@ApiModelProperty(name="层级")
	private Integer level;
	
	@ApiModelProperty(name="父级code")
	private String parent_id;
	
	@ApiModelProperty(name="顶级code")
	private String top_parent_id;
	
	@ApiModelProperty(name="编号名")
	private String aname;
	
	@ApiModelProperty(name="状态")
	private String status;
	
	@ApiModelProperty(name="code")
	private String code;
	
    @TableField(exist = false) 
    private List<MediaTypeVO> children;
    
    
    @Override
    public Object treeId() {
        return code;
    }

    @Override
    public Object treePid() {
        return parent_id;
    }
	@Override
	public void treeChildren(List<MediaTypeVO> children) {
	  if (children != null && children.size() > 0) {
            this.children = children;
        }
	}
	
}
