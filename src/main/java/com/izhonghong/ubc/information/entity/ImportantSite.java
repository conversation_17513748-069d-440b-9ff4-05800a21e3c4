package com.izhonghong.ubc.information.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_important_site")
public class ImportantSite implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("code")
    private String code;

    /**
     * 站点名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属组织ID
     */
    @TableField("org_ids")
    private String orgIds;

    /**
     * 所属行业ID
     */
    @TableField("industry_ids")
    private String industryIds;

    /**
     * 所属区域ID
     */
    @TableField("area_ids")
    private String areaIds;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 站点状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 删除状态 0 正常 1删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted")
    private Integer deleted;

    /**
     * 站点ID
     */
    @TableField("site_id")
    private Integer siteId;

    /**
     * 媒体类型名称
     */
    @TableField("code_name")
    private String codeName;
}
