package com.izhonghong.ubc.information.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.izhonghong.ubc.information.util.TreeUtil;

/**
 * <p>
 *  全国区域信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_area_info")
public class AreaInfo implements TreeUtil.TreeAble<AreaInfo>, Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("name")
    private String name;

    /**
     * 区域编号
     */
    @TableField("code")
    private String code;

    /**
     * 父级区域编号
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     * 等级
     */
    @TableField("level")
    private Integer level;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;
    
    /**
     * 境内境外
     */
    @TableField("types")
    private Integer types;

    @TableField(exist = false)
	private List<AreaInfo> children;

	@Override
	public String treeId() {
		
		return code;
	}

	@Override
	public String treePid() {
		
		return parentCode;
	}

	@Override
	public void treeChildren(List<AreaInfo> children) {
		if (children != null && children.size() > 0) {
            this.children = children;
        }
		
	}


}
