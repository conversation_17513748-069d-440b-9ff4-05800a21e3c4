package com.izhonghong.ubc.information.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_operate_record")
public class OperateRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作模块
     **/
    @TableField(value = "operate_module")
    private String operateModule;

    /**
     * 操作者id
     **/
    @TableField("operator_id")
    private Integer operatorId;

    /**
     * 操作者所属组织
     **/
    @TableField("operator_orgId")
    private Integer operatorOrgId;

    /**
     * 操作者名字(登录名)
     **/
    @TableField("operator_name")
    private String operatorName;

    /**
     * 操作者的用户类型
     **/
    @TableField("operator_usertype")
    private Integer operatorUsertype;

    /**
     * 操作类型,1删除 2修改 3登录 4新增 5查询
     */
    @TableField("operate_type")
    private Integer operateType;

    /**
     * 操作时间
     **/
    @TableField("operate_time")
    private Date operateTime;

    /**
     * 操作结果,1成功/0失败
     **/
    @TableField("operate_result")
    private Boolean operateResult;

    /**
     * 如果操作失败,返回原因
     **/
    @TableField("reason")
    private String reason;
    
    /**
     * 如果操作失败,返回原因
     **/
    @TableField("per_requ_param")
    private String perRequParam;

    /**
     * 操作所属ip
     **/
    @TableField("ip")
    private String ip;

    @TableField("description")
    private String description;

    @TableField("used_time")
    private String usedTime;
    
    public OperateRecord() {
    	
    };

    public OperateRecord(String operateModule, Integer operatorId, Integer operatorOrgId, String operatorName, Integer operatorUsertype, Integer operateType, Boolean operateResult, String reason, String ip, String description, String usedTime,String perRequParam) {
        this.operateModule = operateModule;
        this.operatorId = operatorId;
        this.operatorOrgId = operatorOrgId;
        this.operatorName = operatorName;
        this.operatorUsertype = operatorUsertype;
        this.operateType = operateType;
        this.operateResult = operateResult;
        this.reason = reason;
        this.ip = ip;
        this.description = description;
        this.usedTime = usedTime;
        this.perRequParam = perRequParam;
    }
}
