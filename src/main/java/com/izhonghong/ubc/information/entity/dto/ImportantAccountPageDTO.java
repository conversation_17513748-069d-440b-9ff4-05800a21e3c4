package com.izhonghong.ubc.information.entity.dto;

import lombok.Data;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date 2023-07-05 14:38:11
 */
@Data
public class ImportantAccountPageDTO {

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 账号名称
     */
    private String name;

    /**
     * 账号UID
     */
    private String uid;

    /**
     * 所属组织ID
     */
    private Integer orgId;

    /**
     * 所属行业ID
     */
    private Integer industryId;

    /**
     * 所属区域ID
     */
    private Integer areaId;

    private Integer status;

    private Integer pageNo;

    private Integer pageSize;
}
