package com.izhonghong.ubc.information.entity;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.util.StringUtil;

import lombok.Data;

/**
 * <AUTHOR>
 *      媒体库
 * **/
@Data
public class MediaLibraryInformationSourceBean extends InformationSourceBean{

	/**
	 * 请求类型
	 * 1：修改，0：新增
	 * **/
	private Integer requestType;

	private List<MediaBaseDTO> meidaList;

	public JSONObject getMediaLibraryList() {
		JSONObject json = new JSONObject();
		json.put("data", meidaList);
		return json;
	}

	public void setMediaLibraryList(List<MediaBaseDTO> mediaBaseList) {
		this.meidaList = mediaBaseList;
	}

	public void setMediaLibraryList(MediaBaseDTO mediaBaseDTO) {
		List<MediaBaseDTO> mediaLibraryList = this.meidaList;
		if(StringUtil.isListNull(mediaLibraryList)) {
			mediaLibraryList = new ArrayList<MediaBaseDTO>();
		}
		String area = mediaBaseDTO.getArea();
		if(!StringUtils.isEmpty(area)) {
			area = cleanArea(area);
			mediaBaseDTO.setArea(area);
		}
		mediaLibraryList.add(mediaBaseDTO);
		this.meidaList = mediaLibraryList;
	}



}
