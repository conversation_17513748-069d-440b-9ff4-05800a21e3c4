package com.izhonghong.ubc.information.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 重点业务账号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_important_account")
public class ImportantAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 平台类型
     */
    @TableField("platform_type")
    private Integer platformType;

    /**
     * 账号名称
     */
    @TableField("name")
    private String name;

    /**
     * 账号UID
     */
    @TableField("uid")
    private String uid;

    /**
     * 所属组织ID
     */
    @TableField("org_ids")
    private String orgIds;

    /**
     * 所属行业ID
     */
    @TableField("industry_ids")
    private String industryIds;

    /**
     * 所属区域ID
     */
    @TableField("area_ids")
    private String areaIds;

    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 删除状态 0 正常 1删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted")
    private Integer deleted;


}
