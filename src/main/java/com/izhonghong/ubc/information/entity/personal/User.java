package com.izhonghong.ubc.information.entity.personal;

import lombok.Data;

import java.util.Date;

/**
 * 
 * @Description: User
 * <AUTHOR>  
 * @date 2018-08-24
 * @version  1.0.0
 */

@Data
public class User {
    private Integer id;

    private String loginName;

    private String password;
    //次数
    private Integer attempts;

    private Date lasttime;
    
    private String username;
    
    private String sex;
    
    private Integer status;
    //使用期限_起始时间
    private Date start_time;
    //使用期限_结束时间
    private Date end_time;

    private String phone;

    private String email;

    private String weixin_number;

    private String openid;

    private String qq_number;

    private Integer deptid;

    private Integer orgid;

    private String clientsign;
    
    private Integer first_time;
    
    //*********老版差异新增
    private Integer isweixin;
    private Integer isemail;
    private Integer issms;
    private Integer pid;
    private Integer type;
}