package com.izhonghong.ubc.information.entity.dto;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-16
 */
@Data
public class OperationLogDTO {


    /**
     * 主键id
     */
	@ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 操作模块
     */
	@ApiModelProperty(value = "操作模块")
    private String operateModule;

    /**
     * 操作人id
     */
	@ApiModelProperty(value = "操作人id")
    private Integer operatorId;

    /**
     * 操作人名称
     */
	@ApiModelProperty(value = "操作人名称")
    private String operatorName;

    /**
     * 用户类型
     */
	@ApiModelProperty(value = "用户类型")
    private Integer operatorUsertype;

    /**
     * 参数
     */
	@ApiModelProperty(value = "参数")
    private String perRequParam;

    /**
     * 操作时间
     */
	@ApiModelProperty(value = "操作时间-开始")
    private String startTime;
	
	@ApiModelProperty(value = "操作时间-结束")
    private String endTime;
	
	

    /**
     * 操作数据id
     */
	@ApiModelProperty(value = "操作数据id")
    private String uuid;

	 /**
     * 页码
     */
	@ApiModelProperty(value = "页码")
    private Integer pageNo;
    
	 /**
     * 每页条数
     */
	@ApiModelProperty(value = "每页条数")
    private Integer pageSize;

}
