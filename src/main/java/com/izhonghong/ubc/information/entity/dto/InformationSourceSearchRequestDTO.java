package com.izhonghong.ubc.information.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 信息源搜索请求体DTO
 * 用于接收POST请求的JSON请求体
 * 
 * <AUTHOR>
 */
@ApiModel(value = "信息源搜索请求体")
@Data
public class InformationSourceSearchRequestDTO {

    @ApiModelProperty(value = "搜索条件")
    private ConditionDTO condition;

    @ApiModelProperty(value = "分页信息")
    private PaginatorDTO paginator;

    /**
     * 搜索条件DTO
     */
    @ApiModel(value = "搜索条件")
    @Data
    public static class ConditionDTO {
        
        @ApiModelProperty(value = "信息源ID")
        private String k3Id;

        @ApiModelProperty(value = "媒体标签")
        private String mediaTag;

        @ApiModelProperty(value = "信息源名称")
        private String k3IdName;

        @ApiModelProperty(value = "主办单位")
        private String organizer;

        @ApiModelProperty(value = "账号名称")
        private String name;

        @ApiModelProperty(value = "用户ID")
        private String uid;

        @ApiModelProperty(value = "媒体信息标签：0=非媒体, 1=媒体")
        private Integer mediaInfoTag;

        @ApiModelProperty(value = "媒体级别：央级、省级、市级、区级、其他")
        private String mediaLevel;

        @ApiModelProperty(value = "微博认证类型：金V、橙V、黄V")
        private String weiboVerifyType;

        @ApiModelProperty(value = "账号认证类型：政务、机构、企业、个人、未认证")
        private String verifyType;

        @ApiModelProperty(value = "所属行业")
        private String industry;

        @ApiModelProperty(value = "粉丝数范围起始")
        private Integer followersCountRangeFrom;

        @ApiModelProperty(value = "粉丝数范围结束")
        private Integer followersCountRangeTo;

        @ApiModelProperty(value = "大V标签：0=否, 1=是")
        private Integer bigVLabel;

        @ApiModelProperty(value = "IP位置")
        private String ipLocation;

        @ApiModelProperty(value = "信息源地区")
        private String area;

        @ApiModelProperty(value = "实施状态")
        private Integer status;

        @ApiModelProperty(value = "媒体类型")
        private String mediaType;

        @ApiModelProperty(value = "信息源编号")
        private String code;

        @ApiModelProperty(value = "媒体主页")
        private String webUrl;

        @ApiModelProperty(value = "备注")
        private String remarks;
    }

    /**
     * 分页信息DTO
     */
    @ApiModel(value = "分页信息")
    @Data
    public static class PaginatorDTO {
        
        @ApiModelProperty(value = "起始位置", required = true)
        private Integer from;

        @ApiModelProperty(value = "页面大小", required = true)
        private Integer size;

        @ApiModelProperty(value = "排序规则")
        private List<SortDTO> sorts;
    }

    /**
     * 排序规则DTO
     */
    @ApiModel(value = "排序规则")
    @Data
    public static class SortDTO {
        
        @ApiModelProperty(value = "排序字段", required = true)
        private String field;

        @ApiModelProperty(value = "排序方向：asc/desc", required = true)
        private String order;
    }
}
