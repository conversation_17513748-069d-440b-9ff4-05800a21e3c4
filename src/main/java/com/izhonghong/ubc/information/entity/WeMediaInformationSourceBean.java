package com.izhonghong.ubc.information.entity;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.util.StringUtil;

import lombok.Data;

/**
 * <AUTHOR>
 *      自媒体号
 * **/
@Data
public class WeMediaInformationSourceBean extends InformationSourceBean{

	
	private List<MediaBaseDTO> wechatMeidaList ; 
	
	private List<MediaBaseDTO> weiboMeidaList ; 
	
	private List<MediaBaseDTO> toutiaoMeidaList ;

	private List<MediaBaseDTO> otherMediaList ;


	public JSONObject getWeMediaParamBody() {
		JSONObject json = new JSONObject();
		JSONObject params = new JSONObject();
		params.put("wechat", this.wechatMeidaList);
		params.put("weibo", this.weiboMeidaList);
		params.put("toutiao", this.toutiaoMeidaList);
		params.put("other", this.otherMediaList);
		json.put("params",params );
		return json;
	}
	
	public void setWeMediaList(MediaBaseDTO mediaBaseDTO) {
		
		if(mediaBaseDTO.getMediaTag().contains(SourceTypeMenu.TOU_TIAO.getValue())) {
			if(StringUtil.isListNull(this.toutiaoMeidaList)) {
				this.toutiaoMeidaList = new ArrayList<MediaBaseDTO>();
			}
			this.toutiaoMeidaList.add(mediaBaseDTO);
		}else if(SourceTypeMenu.WE_CHAT.getValue().equals(mediaBaseDTO.getMediaTag())) {
			if(StringUtil.isListNull(this.wechatMeidaList)) {
				this.wechatMeidaList = new ArrayList<MediaBaseDTO>();
			}
			this.wechatMeidaList.add(mediaBaseDTO);
		}else if(SourceTypeMenu.WEI_BO.getValue().equals(mediaBaseDTO.getMediaTag())){
			if(StringUtil.isListNull(this.weiboMeidaList)) {
				this.weiboMeidaList = new ArrayList<MediaBaseDTO>();
			}
			this.weiboMeidaList.add(mediaBaseDTO);
		} else {
			if (StringUtil.isListNull(this.otherMediaList)) {
				this.otherMediaList = new ArrayList<MediaBaseDTO>();
			}
			this.otherMediaList.add(mediaBaseDTO);
		}
	}
	
}
