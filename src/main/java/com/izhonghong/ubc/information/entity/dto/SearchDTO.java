package com.izhonghong.ubc.information.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @
 * **/
@Data
public class SearchDTO {
	
	@ApiModelProperty(name = "用户名(支持模糊)")
	private String name;
	
	@ApiModelProperty(name = "用户类型：2微信，3微博，4头条")
	private String sourceType;
	
	@ApiModelProperty(name = "开始行")
	private long startRow ;
	
	
	@ApiModelProperty(name = "每页条数")
	private long size;
	
	@ApiModelProperty(name = "省份(支持模糊)")
	private String province	;
	
	@ApiModelProperty(name = "城市(支持模糊)")
	private String city;
	
	
	@ApiModelProperty(name = "媒体类型 1001：大V号，1002政务号，1003媒体号。暂时只有微博有此分类")
	private String mediaType ;

}
