package com.izhonghong.ubc.information.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

public class RequestDTO {

    @NoArgsConstructor
    @Data
    public class Request {

        @JsonProperty("data")
        private List<DataDTO> data;

        @NoArgsConstructor
        @Data
        public  class DataDTO {
            @JsonProperty("name")
            private String name;
            @JsonProperty("new_code")
            private String newCode;
            @JsonProperty("organizer")
            private String organizer;
            @JsonProperty("area")
            private String area;
            @JsonProperty("web_url")
            private String webUrl;
            @JsonProperty("media_level")
            private String mediaLevel;
            @JsonProperty("industry")
            private String industry;
            @JsonProperty("media_info_tag")
            private Integer mediaInfoTag;
            @JsonProperty("remarks")
            private String remarks;
        }
    }

}
