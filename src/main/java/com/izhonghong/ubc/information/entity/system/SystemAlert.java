package com.izhonghong.ubc.information.entity.system;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_system_alert")
public class SystemAlert implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 系统模块
     */
    @TableField("system_model")
    private String systemModel;

    /**
     * 预警模式  {@link SystemAlertTypeMenu}
     */
    @TableField("alert_type")
    private Integer alertType;

    /**
     * 企业微信机器人地址
     */
    @TableField("webhook")
    private String webhook;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private Integer phoneNumber;

    /**
     * 邮箱号
     */
    @TableField("email_number")
    private String emailNumber;

    /**
     * 预警内容
     */
    @TableField("alert_content")
    private String alertContent;


}
