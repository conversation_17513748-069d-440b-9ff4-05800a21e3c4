package com.izhonghong.ubc.information.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *      媒体库
 * **/
@Data
public class MediaLibraryDTO extends MediaBaseDTO{
	@ApiModelProperty(value="媒体名称")
	private String name;
	
	@ApiModelProperty(value = "媒体主页")
	private String web_url;
		
	@ApiModelProperty(value = "媒体性质")
	private String media;
	
	@ApiModelProperty(value = "媒体分类")
	private String code;
	
	@ApiModelProperty(value = "媒体类型：微博微信网站等一级分类")
	private String media_type;
	
	@ApiModelProperty(value = "具体的地方，比如 广东省:深圳市:南山区")
	private String area;
	
	@ApiModelProperty(value = "信息源状态 1采集中 0待实施 -1不可用")
	private Integer status;
	
	@ApiModelProperty(name="页码")
	private Integer pageNo;
	
	@ApiModelProperty(name="每页条数")
	private Integer size;
	
	@ApiModelProperty(name="主办单位")
	private String organizer;

	@ApiModelProperty(name = "媒体信息")
	private String media_info;

	@ApiModelProperty(name = "媒体等级")
	private String mediaLevel;

	@ApiModelProperty(name = "所属行业")
	private String industry;

	@ApiModelProperty(name="全部")
	private Boolean isAll;

	@ApiModelProperty(name="页码")
	private Integer page;

	@ApiModelProperty(name="新媒体分类")
	private String newCode;

	@ApiModelProperty(name="媒体标签")
	private Integer mediaInfoTag;

	@ApiModelProperty(name="网站域名")
	private String siteDomain;

	@ApiModelProperty(value = "新信息源编号")
	private String new_code;

	@ApiModelProperty(value = "ES索引名称")
	private String es_index_name;

}
