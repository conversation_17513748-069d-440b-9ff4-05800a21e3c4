package com.izhonghong.ubc.information.entity.dto;

import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *      信息源媒体的基础字段
 * **/
@ApiModel(value="信息源基础字段")
@Data
public class MediaBaseDTO {

	@ApiModelProperty(value = "媒体名称")
	private String name;

	@ApiModelProperty(value = "媒体id")
	private String id;

	@ApiModelProperty(value = "账号，必填")
	private String account;

	/**
	 * @see SourceTypeMenu
	 **/
	@ApiModelProperty(value = "客户类型值中文：微博、微信、头天")
	private String sourceType;

	@ApiModelProperty(value = "媒体类型值中文：微博、抖音")
	private String mediaTag;

	@ApiModelProperty(value = "媒体类型")
	private Integer platformType;

	@ApiModelProperty(value = "媒体主页")
	private String web_url;

	@ApiModelProperty(value = "微博、头条的媒体主页")
	private String homeUrl;

	@ApiModelProperty(value = "id")
	private String uid; //id

	@ApiModelProperty(value = "媒体性质")
	private String media;

	@ApiModelProperty(value = "生日(个人账号)")
	private String birthday;

	@ApiModelProperty(value = "label")
	private String label; //标签

	@ApiModelProperty(value = "媒体类型：微博微信网站等一级分类")
	private String media_type;

	@ApiModelProperty(value = "具体的地方，比如 广东省:深圳市:南山区")
	private String area;

	@ApiModelProperty(value = "信息源编号")
	private String code;

	@ApiModelProperty(value = "信息源编号")
	private List<String> codeList;

	@ApiModelProperty(value = "主办单位")
	private String organizer;

	@ApiModelProperty(value = "信息源状态 1采集中 0待实施 -1不可用")
	private Integer status;

	@ApiModelProperty(value = "备注")
	private String remarks;

	@ApiModelProperty(value = "账号类别")
	private String accountType;

	@ApiModelProperty(value = "账号级别：影响采集频率")
	private String accountLevel;

	@ApiModelProperty(value = "排序字段")
	private String sortField;

	@ApiModelProperty(value = "升降序")
	private String sort;

	@ApiModelProperty(value = "认证信息")
	private String verifiedInfo;

	@ApiModelProperty(value = "信息源ID")
	private Integer k3_id;

	@ApiModelProperty(value = "大V标识")
	private String businessCode;

	@ApiModelProperty(value = "更新类型 0常用 1维护")
	private Integer refreshType;

	@ApiModelProperty(name = "媒体信息")
	private String media_info;

	@ApiModelProperty(name = "媒体等级")
	private String mediaLevel;

	@ApiModelProperty(name = "所属行业")
	private String industry;

	@ApiModelProperty(name="全部")
	private Boolean isAll;

	@ApiModelProperty(name="页码")
	private Integer page;

	@ApiModelProperty(name="新媒体分类")
	private String new_code;

	@ApiModelProperty(name="媒体标签")
	private String media_info_tag;

	@ApiModelProperty(name="网站域名")
	private String siteDomain;

	@ApiModelProperty(value = "新信息源编号")
	private String newCode;

	@ApiModelProperty(value = "ES索引名称")
	private String es_index_name;

	@ApiModelProperty(value ="等级")
	private String level;

	@ApiModelProperty(value = "描述")
	private String description;

	@ApiModelProperty(value="创造时间")
	private String create_at;

	@ApiModelProperty(value="更新时间")
	private String update_at;

	@ApiModelProperty(value = "父级code")
	private String parent_id;

	@ApiModelProperty(value = "是否删除")
	private String is_delete;

	@ApiModelProperty(value = "排序字段")
	private String sort_order;

	@ApiModelProperty(value = "自媒体平台")
	private String new_name;

	@ApiModelProperty(value = "大小")
	private Integer size;
}
