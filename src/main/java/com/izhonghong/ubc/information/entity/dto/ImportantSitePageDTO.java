package com.izhonghong.ubc.information.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date 2023-07-05 14:38:42
 */
@Data
public class ImportantSitePageDTO {

    /**
     * 站点名称
     */
    private String name;

    /**
     * 站点ID
     */
    private Integer siteId;

    /**
     * 所属组织ID
     */
    private Integer orgId;

    /**
     * 所属行业ID
     */
    private Integer industryId;

    /**
     * 所属区域ID
     */
    private Integer areaId;

    private String code;

    private Integer status;

    private Integer pageNo;

    private Integer pageSize;

}
