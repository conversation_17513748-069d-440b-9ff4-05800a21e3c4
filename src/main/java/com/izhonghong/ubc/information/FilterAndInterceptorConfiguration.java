/**
 * 
 */
package com.izhonghong.ubc.information;

import com.izhonghong.ubc.security.filter.SecurityFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @2019年5月28日上午11:04:46
 */
@Configuration
public class FilterAndInterceptorConfiguration {
	
	/**
	 * 鉴权拦截器
	 * @return
	 */
//	@Bean
//	 public FilterRegistrationBean authenticationFilterRegistrationBean() {
//		FilterRegistrationBean authenticationFilterRegistrationBean = new FilterRegistrationBean();
//		authenticationFilterRegistrationBean.setFilter(new SecurityFilter());
//		authenticationFilterRegistrationBean.setName("securityFilter");
//		authenticationFilterRegistrationBean.addUrlPatterns("/*");
//		authenticationFilterRegistrationBean.setOrder(0);
//		return authenticationFilterRegistrationBean;
//	 }

}
