package com.izhonghong.ubc.information.factory;

import com.izhonghong.ubc.information.common.enums.SourceDataTypeEnum;
import com.izhonghong.ubc.information.service.abstracts.AbstractsInformationSourceDataService;
import com.izhonghong.ubc.information.service.impl.abstracts.WebSpiderInformationSourceDataServiceImpl;

public class InformationSourceDataServiceFactory {
	
	public static AbstractsInformationSourceDataService getAbstractsInformationSourceDataService(Integer type) {
		if(SourceDataTypeEnum.WEB_SPIDER.getCode().equals(type)) {
			return new WebSpiderInformationSourceDataServiceImpl();
		}
		return null;
	}

}
