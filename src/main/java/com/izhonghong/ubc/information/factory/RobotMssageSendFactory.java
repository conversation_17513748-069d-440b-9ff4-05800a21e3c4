package com.izhonghong.ubc.information.factory;

import com.izhonghong.ubc.information.common.enums.RobotTypeMenu;
import com.izhonghong.ubc.information.service.abstracts.AbstractsRobotMssageSendService;
import com.izhonghong.ubc.information.service.impl.abstracts.EnterpriseWeChatMessageSendServiceImpl;

public class RobotMssageSendFactory {
	
	public static AbstractsRobotMssageSendService creatRobot(Integer type) {
		
		if(RobotTypeMenu.EE_WE_CHAT.getCode().equals(type)) {
			return new EnterpriseWeChatMessageSendServiceImpl();
		}
		
		return null;
		
	}

}
