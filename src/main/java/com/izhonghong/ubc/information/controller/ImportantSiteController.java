package com.izhonghong.ubc.information.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izhonghong.ubc.information.entity.dto.ImportantSiteDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantSitePageDTO;
import com.izhonghong.ubc.information.entity.vo.ImportantSiteVO;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import com.izhonghong.ubc.information.service.ImportantSiteService;
import com.izhonghong.ubc.information.util.Assert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2023-07-05
 */
@Api(tags = "重点业务网站接口")
@Slf4j
@RestController
@RequestMapping("/importantSite")
public class ImportantSiteController {

    @Autowired
    private ImportantSiteService importantSiteService;

    @ApiOperation("批量保存网站库")
    @PostMapping("batchSaveSites")
    public Result batchSaveSites(@RequestBody List<ImportantSiteDTO> sites) {
        Assert.require(CollUtil.isNotEmpty(sites), "参数不能为空");

        log.info("批量保存网站库，参数：{}", JSON.toJSONString(sites));
        boolean result = importantSiteService.batchSaveSites(sites);
        if (result) {
            return Result.successResult();
        } else {
            return Result.fail(StatusCode.SYS_COMMON_OPERATION_FAIL);
        }
    }

    @ApiOperation("分页查询业务网站库")
    @PostMapping("findImportantSitePage")
    public Result findImportantSitePage(@RequestBody ImportantSitePageDTO importantSitePageDTO) {
        IPage<ImportantSiteVO> importantSiteVoIpage = importantSiteService.findImportantSitePage(importantSitePageDTO);
        return Result.successResult().setData(importantSiteVoIpage);
    }
}
