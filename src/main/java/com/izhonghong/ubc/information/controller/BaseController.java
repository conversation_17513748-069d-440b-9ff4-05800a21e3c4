package com.izhonghong.ubc.information.controller;

import com.alibaba.fastjson.JSON;
import com.izhonghong.ubc.information.constant.SystemModuleEnum;
import com.izhonghong.ubc.information.entity.OperationLog;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.service.OperationLogService;
import com.izhonghong.ubc.information.service.personal.UserService;
import com.izhonghong.ubc.information.util.components.SystemAlertUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

/**
 * 控制类基类
 */
@RestController
@Slf4j
public abstract class BaseController {

    protected final static Logger LOGGER = LoggerFactory.getLogger(BaseController.class);

    @Autowired
    protected UserService userService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private SystemAlertUtils systemAlertUtils;
    
    @Autowired
    private OperationLogService operationLogService;

    protected User getLoginUser() {
        User user = userService.getLoginUser(request);
        return user;
    }
    protected String getAccessToken() {
    	String token = request.getHeader("authorization");
        return token;
    }
    
    protected void saveOperationLog(String operateModule,String uuid,String perRequParam) {
    	try {
	    	User user = getLoginUser();
            log.info("saveOperationLog user is " + JSON.toJSONString(user));
	    	OperationLog operationLog = new OperationLog();
            operationLog.setCreatedAt(new Date());
            operationLog.setOperatorName(user.getLoginName());
            operationLog.setOperatorId(user.getId());
            operationLog.setOperatorUsertype(user.getType());
            operationLog.setOperateModule(operateModule);
            operationLog.setUuid(uuid);
	    	perRequParam = perRequParam == null ? "" :perRequParam;
            operationLog.setPerRequParam(perRequParam);
            log.info("saveOperationLog OperationLog is {}"+ JSON.toJSONString(log));
	    	operationLogService.save(operationLog);
    	}catch (Exception e) {
    		systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL, "操作日志保存异常  \n"+e);
		}
		
	}
    
    /***
           * 错误预警
     * **/
    protected void errorAlert(SystemModuleEnum systemEnum,String message) {
    	systemAlertUtils.warnInformation(systemEnum, message);
    }
}
