package com.izhonghong.ubc.information.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izhonghong.ubc.information.aop.OperateLog;
import com.izhonghong.ubc.information.common.enums.SourceStatusEnum;
import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;
import com.izhonghong.ubc.information.config.WebCrawlerConfig;
import com.izhonghong.ubc.information.constant.BusinessConstant;
import com.izhonghong.ubc.information.constant.CommonConstant;
import com.izhonghong.ubc.information.elasticsearch.ElasticSearchAPI;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDeleteDTO;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.entity.vo.AccountInfoVo;
import com.izhonghong.ubc.information.entity.vo.MediaTypeVO;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import com.izhonghong.ubc.information.service.AreaInfoService;
import com.izhonghong.ubc.information.service.MediaService;
import com.izhonghong.ubc.information.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags="weMedia", value="自媒体号")
@RestController
@RequestMapping("/weMedia")
public class WeMediaController extends BaseController {

	@Autowired
	private ElasticSearchAPI elasticSearchApi;

	@Autowired
	private WebCrawlerConfig webCrawlerConfig;

	@Autowired
	private ExportUtils exportUtils;

	@Autowired
	private MediaService mediaService;

	private Map<String, String> mediaType;

	@Autowired
	private AreaInfoService areaInfoService;

	@Autowired
	private RedisUtil redisUtil;


	/**
	 * 添加
	 */
	@ApiOperation(value = "自媒体号的修改或新增", notes = "自媒体号的修改或新增")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_UPDATE, operateDescription = "自媒体号的修改或新增")
	@PostMapping("saveOrUpdate")
	public Result saveOrUpdate(@RequestBody MediaBaseDTO WeMediaDto) {
		if (StringUtils.isEmpty(WeMediaDto.getMediaTag())
				|| StringUtils.isEmpty(WeMediaDto.getName())
				|| StringUtils.isEmpty(WeMediaDto.getCode())
				|| StringUtils.isEmpty(WeMediaDto.getUid())
		) {
			return Result.fail(StatusCode.SYS_COMMON_INVALID_PARAM);
		}
		/*if(!SourceTypeMenu.checkType(WeMediaDTO.getSourceType())) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM,"超出了媒体分类范围");
		}*/
		if (StringUtils.isEmpty(WeMediaDto.getArea()) || !StringUtils.isEmpty(WeMediaDto.getArea()) && !areaInfoService.checkAreaName(WeMediaDto.getArea())) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM, "区域参数不合法");
		}
		if ((!SourceTypeMenu.WE_CHAT.getValue().equals(WeMediaDto.getSourceType()) && !StringUtil.isHttpUrl(WeMediaDto.getHomeUrl()))
				|| WeMediaDto.getName().length() > 30) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM);
		}
		if (mediaType == null) {
			mediaType = mediaService.mediaTypeMap();
		}
		if (StringUtils.isEmpty(WeMediaDto.getStatus())) {
			WeMediaDto.setStatus(0);
		}
		saveOperationLog("自媒体号新增或修改", WeMediaDto.getUid(), WeMediaDto.toString());
		WeMediaInformationSourceBean informationSourceBean = new WeMediaInformationSourceBean();
		WeMediaDto.setArea(informationSourceBean.cleanArea(WeMediaDto.getArea()));
		WeMediaDto.setAccountLevel(mediaType.get(WeMediaDto.getCode()));
		informationSourceBean.setWeMediaList(WeMediaDto);
		JSONObject result = elasticSearchApi.WeMediaSaveOrUpdate(informationSourceBean);

		return Result.successResult().setData(result);

	}

	/**
	 * 添加
	 */
	@ApiOperation(value = "自媒体号的批量修改", notes = "自媒体号的批量修改")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_UPDATE, operateDescription = "自媒体号的批量修改")
	@PostMapping("updateBatch")
	public Result updateBatch(@RequestBody List<MediaBaseDTO> weMediaList) {
		if (StringUtil.isListNull(weMediaList)) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM);
		}
		long checkAreaCount = weMediaList.stream().filter(t -> !StringUtils.isEmpty(t.getArea()) && !areaInfoService.checkAreaName(t.getArea())).count();
		if (checkAreaCount > 0) {
			return Result.fail(StatusCode.SYS_DATA_INVALID_PARAM, "区域名称异常");
		}
		if (mediaType == null) {
			mediaType = mediaService.mediaTypeMap();
		}
		WeMediaInformationSourceBean informationSourceBean = new WeMediaInformationSourceBean();
		weMediaList.forEach(t -> {
			t.setAccountLevel(mediaType.get(t.getCode()));
			if (StringUtils.isEmpty(t.getArea())) {
				t.setArea(null);
			} else {
				t.setArea(informationSourceBean.cleanArea(t.getArea()));
			}
			if (StringUtils.isEmpty(t.getAccountLevel())) {
				t.setAccountLevel(null);
			}
			saveOperationLog("自媒体号的批量修改", t.getUid(), t.toString());
		});
		List<MediaBaseDTO> wechatMeidaList = weMediaList.stream().filter(t -> SourceTypeMenu.WE_CHAT.getValue().equals(t.getSourceType())).collect(Collectors.toList());

		List<MediaBaseDTO> weiboMeidaList = weMediaList.stream().filter(t -> SourceTypeMenu.WEI_BO.getValue().equals(t.getSourceType())).collect(Collectors.toList());

		List<MediaBaseDTO> toutiaoMeidaList = weMediaList.stream().filter(t -> t.getSourceType().contains(SourceTypeMenu.TOU_TIAO.getValue())).collect(Collectors.toList());


		informationSourceBean.setWechatMeidaList(wechatMeidaList);
		informationSourceBean.setWeiboMeidaList(weiboMeidaList);
		informationSourceBean.setToutiaoMeidaList(toutiaoMeidaList);

		JSONObject result = elasticSearchApi.WeMediaSaveOrUpdate(informationSourceBean);

		return Result.successResult().setData(result);

	}

	@ApiOperation(value = "媒体列表的分页查询", notes = "媒体列表的分页查询")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的分页查询")
	@PostMapping("selectPage")
	public Result selectPage(@RequestBody WeMediaDTO weMediaDTO) {
		JSONObject result = elasticSearchApi.selectInformation(weMediaDTO);
		return Result.successResult().setData(result);
	}


	@ApiOperation(value = "自媒体列表的分页查询", notes = "自媒体列表的分页查询")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "自媒体列表的分页查询")
	@PostMapping("selectAccountPage")
	public Result<Page<AccountInfoVo>> selectAccountPage(@RequestBody WeMediaDTO weMediaDTO) {
		Page<AccountInfoVo> data = elasticSearchApi.selectAccountPage(weMediaDTO);
		return Result.<Page<AccountInfoVo>>successResult().setData(data);
	}


	@ApiOperation(value = "媒体列表的导入", notes = "媒体列表的导入")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的导入")
	@PostMapping("uploadMedia")
	public Result uploadMedia(@RequestParam("files") MultipartFile files, HttpServletRequest request, HttpServletResponse response) {

		User user = getLoginUser();
		String name = files.getOriginalFilename();
		//不是以
		if (!name.endsWith(".xlsx") && !name.endsWith(".xls")) {
			return Result.fail(StatusCode.SYS_COMMON_EXCEL_IMPORT_ERR, "文件格式不对,请上传xlsx/xls格式文件");
		}
		JSONObject json = new JSONObject();
		try {
			json = mediaService.importUpdateBatch(name, files.getInputStream(), user, 0);
			if (json.getBoolean("flage")) {
				return Result.successResult();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return Result.fail(StatusCode.SYS_COMMON_EXCEL_IMPORT_ERR, json.toJSONString());

	}

	@ApiOperation(value = "媒体列表的导出", notes = "媒体列表的导出")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的导出")
	@PostMapping("exportMedia")
	public Result exportMedia(@RequestBody WeMediaDTO WeMediaDto, HttpServletRequest request, HttpServletResponse response) {
		request.getSession();
		WeMediaDto.setSize(3000);
		if (!StringUtils.isEmpty(WeMediaDto.getMedia_type())) {
			if (mediaType == null) {
				mediaType = mediaService.mediaTypeMap();
			}
			WeMediaDto.setAccountLevel(mediaType.get(Integer.valueOf(WeMediaDto.getMedia_type())));
		}
		JSONObject result = elasticSearchApi.selectInformation(WeMediaDto);
		User user = getLoginUser();
		if (result == null) {
			return Result.fail(StatusCode.SYS_COMMON_EXCEL_EXPORT_ERR);
		}
		JSONArray dataArray = result.getJSONArray("data");
		if (dataArray == null || dataArray.size() < 1) {
			return Result.fail(StatusCode.SYS_COMMON_EXCEL_EXPORT_DATA_ERR);
		}

		Map<String, String> mediaTypeMap = getMediaTypes2Map();

		Map<Integer, String> statusMap = SourceStatusEnum.getMap();

		String fileName = "自媒体号列表导出_" + user.getUsername() + "_" + DateUtil.getCurrDate();
		String[] sheets = new String[]{"平台类型", "媒体名称", "首页地址", "所属区域", "添加时间", "媒体类型", "粉丝数", "粉丝量级"
				, "认证信息", "更新类型", "采集状态", "备注"};

		String[][] values = new String[dataArray.size()][sheets.length];
		for (int i = 0; i < dataArray.size(); i++) {
			JSONObject dataJson = dataArray.getJSONObject(i);
			values[i][0] = dataJson.getString("sourceType");
			values[i][1] = dataJson.getString("name");
			values[i][2] = dataJson.getString("homeUrl");
			values[i][3] = dataJson.getString("area");
			values[i][4] = dataJson.getString("updateTime");

			String code = dataJson.getString("code");
			String mediaType = "";
			if (StringUtil.isNotEmpty(code)) {
				mediaType = mediaTypeMap.get(code);
			}
			values[i][5] = mediaType;
			// 粉丝数 - followersCount
			values[i][6] = dataJson.getString("followersCount");
			// 粉丝量级 - businessCode bigV-大B；为空或没有该值表示非大V
			String logotype = "非大V";
			if (dataJson.containsKey("businessCode") && "bigV".equals(dataJson.get("businessCode"))) {
				logotype = "大V";
			}
			values[i][7] = logotype;
			// 认证信息 - verifiedInfo
			String verify = "";
			if (dataJson.containsKey("verifiedInfo")) {
				verify = dataJson.getString("verifiedInfo");
			}
			values[i][8] = verify;
			// 更新类型 - refreshType refreshType =0显示常用，  refreshType =1或者空， 显示维护
			String refreshType = "维护";
			if (dataJson.containsKey("refreshType") && null != dataJson.get("refreshType") && dataJson.getInteger("refreshType").equals(0)) {
				refreshType = "常用";
			}
			values[i][9] = refreshType;
			values[i][10] = statusMap.get(dataJson.getInteger("status"));
			values[i][11] = dataJson.getString("remarks");
		}
		String title = "自媒体信息列表";

		ExcelExportUtils.createWorkbook(fileName, title, sheets, values, response, request);

		return Result.successResult();

	}

	private Map<String, String> getMediaTypes2Map() {

		Map<String, String> map = new HashMap<>();
		List<MediaTypeVO> mediaTypeVos;
		Object cache = redisUtil.get(CommonConstant.MEDIA_TYPE_TREE_KEY);
		if (cache != null) {
			mediaTypeVos = JSON.parseArray(cache.toString(), MediaTypeVO.class);
		} else {
			MediaLibraryDTO mediaLibraryDTO = new MediaLibraryDTO();
			JSONObject mediaType = elasticSearchApi.selectMediaTypeTree(mediaLibraryDTO);
			mediaTypeVos = JSON.parseArray(mediaType.getString("data"), MediaTypeVO.class);
		}

		List<MediaTypeVO> allMediaTypes = getAllMediaTypes(mediaTypeVos);

		if (!CollectionUtils.isEmpty(allMediaTypes)) {
			map = allMediaTypes.stream().collect(Collectors.toMap(MediaTypeVO::getCode, MediaTypeVO::getName, (key1, key2) -> key1));
		}

		return map;
	}

	private List<MediaTypeVO> getAllMediaTypes(List<MediaTypeVO> mediaTypeVos) {
		List<MediaTypeVO> allMediaTypes = new ArrayList<>();

		for (MediaTypeVO vo : mediaTypeVos) {
			allMediaTypes.add(vo);
			List<MediaTypeVO> children = vo.getChildren();
			if (CollectionUtils.isEmpty(children)) {
				continue;
			}
			List<MediaTypeVO> mediaTypes = getAllMediaTypes(children);
			allMediaTypes.addAll(mediaTypes);
		}

		return allMediaTypes;
	}


	@ApiOperation(value = "自媒体号模板导出", notes = "自媒体号模板导出")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "自媒体号模板导出")
	@GetMapping("exportSampleFile")
	public void exportSampleFile(HttpServletRequest request, HttpServletResponse response) {

		request.getSession();
		String rootPath = webCrawlerConfig.getFileBasedir();//存储文件的目录
		String fullPath = rootPath + "/wemedia.xlsx";//文件的位置
		Boolean flag = exportUtils.exportDocument(fullPath, "自媒体号导入模板.xlsx", response);
	}


	@ApiOperation(value = "获取全部的自媒体平台类型", notes = "自媒体平台类型")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "自媒体平台类型")
	@GetMapping("getAllPlatformType")
	public Result getWeMediaPlatformType() {
		MediaLibraryDTO mediaLibraryDTO = new MediaLibraryDTO();
		JSONObject result = elasticSearchApi.getWeMediaPlatformType(mediaLibraryDTO);

		return Result.successResult().setData(result.get("data"));
	}

	@ApiOperation(value = "自媒体账号删除", notes = "自媒体账号删除")
	@PostMapping("delete")
	public Result delete(@RequestBody WeMediaDeleteDTO weMediaDeleteDTO) {
		if (CollUtil.isEmpty(weMediaDeleteDTO.getIndexIds())) {
			return Result.fail(StatusCode.SYS_COMMON_INVALID_PARAM);
		}

		JSONObject result = elasticSearchApi.deleteWeMedia(weMediaDeleteDTO);
		return Result.successResult().setData(result);
	}


}
