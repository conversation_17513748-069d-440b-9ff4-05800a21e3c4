package com.izhonghong.ubc.information.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izhonghong.ubc.information.entity.dto.ImportantAccountDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantAccountPageDTO;
import com.izhonghong.ubc.information.entity.vo.ImportantAccountVO;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import com.izhonghong.ubc.information.service.ImportantAccountService;
import com.izhonghong.ubc.information.util.Assert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
@Api(tags = "业务重点自媒体账号接口")
@Slf4j
@RestController
@RequestMapping("/importantAccount")
public class ImportantAccountController {

    @Autowired
    private ImportantAccountService importantAccountService;

    @ApiOperation("批量保存自媒体账号")
    @PostMapping("batchSaveAccounts")
    public Result batchSaveAccounts(@RequestBody List<ImportantAccountDTO> accountDtos) {
        Assert.require(CollUtil.isNotEmpty(accountDtos), "参数不能为空");

        log.info("批量保存自媒体账号，参数：{}", JSON.toJSONString(accountDtos));
        boolean result = importantAccountService.batchSaveAccounts(accountDtos);
        if (result) {
            return Result.successResult();
        } else {
            return Result.fail(StatusCode.SYS_COMMON_OPERATION_FAIL);
        }
    }

    @ApiOperation("分页查询业务自媒体账号")
    @PostMapping("findImportantAccountPage")
    public Result findImportantAccountPage(@RequestBody ImportantAccountPageDTO importantAccountPageDTO) {
        IPage<ImportantAccountVO> importantSiteVoIpage = importantAccountService.findImportantAccountPage(importantAccountPageDTO);
        return Result.successResult().setData(importantSiteVoIpage);
    }

}
