package com.izhonghong.ubc.information.controller;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.izhonghong.ubc.information.common.enums.WebSpiderNewDataUrlEnum;
import com.izhonghong.ubc.information.constant.CommonConstant;
import com.izhonghong.ubc.information.entity.vo.MediaDetailVO;
import com.izhonghong.ubc.information.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.aop.OperateLog;
import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;
import com.izhonghong.ubc.information.constant.BusinessConstant;
import com.izhonghong.ubc.information.elasticsearch.ElasticSearchAPI;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.entity.vo.MediaTypeVO;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.result.StatusCode;
import com.izhonghong.ubc.information.util.components.SpiderApi;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "Media", value = "媒体管理")
@RestController
@RequestMapping("/media")
public class MediaController extends BaseController {

    @Autowired
    private ElasticSearchAPI elasticSearchApi;

    @Autowired
    private SpiderApi spiderApi;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 分页
     */
    @ApiOperation(value = "媒体列表的分页查询", notes = "媒体列表的分页查询")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的分页查询")
    @PostMapping("selectPage")
    public Result selectPage(@RequestBody MediaLibraryDTO mediaLibraryDTO) {

        JSONObject result = elasticSearchApi.search(mediaLibraryDTO);

        return Result.successResult().setData(result);

    }

    /**
     * 导入
     */
    @ApiOperation(value = "媒体列表的导出", notes = "媒体列表的导出")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的导出")
    @PostMapping("exportMedia")
    public Result exportMedia(@RequestBody MediaLibraryDTO mediaLibraryDTO, HttpServletRequest request, HttpServletResponse response) {

        JSONObject result = elasticSearchApi.search(mediaLibraryDTO);
        User user = getLoginUser();
        String fileName = "媒体列表导出_" + user.getUsername() + "_" + DateUtil.getCurrDate();
        String[] sheets = new String[]{"主办单位", "媒体名称", "首页地址", "所属区域", "媒体性质","媒体信息","媒体等级","所属行业", "采集状态","备注"};
        String[][] values = new String[0][0];
        String title = "媒体库信息列表";
        if ("".equals(mediaLibraryDTO.getSourceType())) {
            title = "自媒体信息列表";
        }
        ExcelExportUtils.createWorkbook(fileName, title, sheets, values, response, request);

        return Result.successResult();

    }

    /**
     * 导入
     */
    @ApiOperation(value = "媒体列表的导入", notes = "媒体列表的导入")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的导入")
    @PostMapping("uploadMedia")
    public Result uploadMedia(@RequestParam("files") MultipartFile files, HttpServletRequest request, HttpServletResponse response) {

        String name = files.getOriginalFilename();
        //不是以
        if (!name.endsWith(".xlsx") && !name.endsWith(".xls")) {
            return Result.fail(StatusCode.SYS_COMMON_EXCEL_IMPORT_ERR, "文件格式不对,请上传xlsx/xls格式文件");
        }


        return Result.successResult();

    }


    @ApiOperation(value = "媒体分类树", notes = "媒体分类树")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体分类树")
    @GetMapping("selectMediaTypeTree")
    public Result selectMediaTypeTree() {
        Object cache = redisUtil.get(CommonConstant.MEDIA_TYPE_TREE_KEY);
        if (cache != null) {
            List<MediaTypeVO> mediaTypeVos = JSON.parseArray(cache.toString(), MediaTypeVO.class);
            return Result.successResult().setData(mediaTypeVos);
        }
        MediaLibraryDTO mediaLibraryDTO = new MediaLibraryDTO();
        JSONObject result = elasticSearchApi.selectMediaTypeTree(mediaLibraryDTO);
        List<MediaTypeVO> list = JSON.parseArray(result.getString("data"), MediaTypeVO.class);
        if (!StringUtil.isListNull(list)) {
            list = (List<MediaTypeVO>) TreeUtil.toTree(list, "0");
            redisUtil.set(CommonConstant.MEDIA_TYPE_TREE_KEY, JSON.toJSONString(list), 24 * 60 * 60);
        }
        return Result.successResult().setData(list);
    }

    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体分类树")
    @GetMapping("selectMediaTypeTreeNew")
    public Result selectMediaTypeTreeNew() {
        Object cache = redisUtil.get(CommonConstant.MEDIA_TYPE_TREE_KEY_NEW);
        if (cache != null) {
            List<MediaTypeVO> mediaTypeVos = JSON.parseArray(cache.toString(), MediaTypeVO.class);
            return Result.successResult().setData(mediaTypeVos);
        }
        MediaLibraryDTO mediaLibraryDTO = new MediaLibraryDTO();
        JSONObject result = elasticSearchApi.selectMediaTypeTree(mediaLibraryDTO);
        List<MediaTypeVO> list = JSON.parseArray(result.getString("data"), MediaTypeVO.class);
        List<MediaTypeVO> newList = new ArrayList<>();
        if (!StringUtil.isListNull(list)) {
            list = (List<MediaTypeVO>) TreeUtil.toTree(list, "1");
            newList.add(list.get(0));
            newList.add(addMediaTypeVO("抖音", "109"));
            newList.add(list.get(1));
            newList.add(addMediaTypeVO("小红书", "110"));
            newList.add(addMediaTypeVO("快手", "112"));
            newList.add(addMediaTypeVO("今日头条", "111"));
            newList.add(list.get(2));
            newList.add(list.get(3));
            newList.add(list.get(4));
            newList.add(list.get(5));
            newList.add(list.get(6));
            newList.add(list.get(7));
            redisUtil.set(CommonConstant.MEDIA_TYPE_TREE_KEY_NEW, JSON.toJSONString(newList), 24 * 60 * 60);
        }
        return Result.successResult().setData(newList);
    }

    private MediaTypeVO addMediaTypeVO(String name, String code) {
        MediaTypeVO mediaTypeVO = new MediaTypeVO();
        mediaTypeVO.setName(name);
        mediaTypeVO.setLevel(2);
        mediaTypeVO.setParent_id("1");
        mediaTypeVO.setTop_parent_id("!");
        mediaTypeVO.setStatus("1");
        mediaTypeVO.setAname(null);
        mediaTypeVO.setCode(code);
        mediaTypeVO.setChildren(new ArrayList<>());
        return mediaTypeVO;
    }


    @ApiOperation(value = "媒体根节点分类", notes = "媒体根节点分类")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体根节点分类")
    @GetMapping("selectMediaRootType")
    public Result selectMediaRootType() {

        MediaLibraryDTO mediaLibraryDTO = new MediaLibraryDTO();
        JSONObject result = elasticSearchApi.selectMediaTypeTree(mediaLibraryDTO);
        List<MediaTypeVO> list = JSON.parseArray(result.getString("data"), MediaTypeVO.class);
        list = list.stream().filter(t -> "1".equals(t.getParent_id())).collect(Collectors.toList());
        return Result.successResult().setData(list);

    }


    @ApiOperation(value = "快捷解析接口", notes = "快捷解析接口")
    @OperateLog(module = BusinessConstant.OPERATE_MODULE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "快捷解析接口")
    @PostMapping("analysisUrl")
    public Result<Object> analysisUrl(@RequestBody MediaBaseDTO dto) {
        Assert.requireNonBlank(dto.getWeb_url(), "url不能为空");
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Integer articleType = StringUtil.getUrlType(dto.getWeb_url());
        if (articleType == null) {
            return Result.fail(StatusCode.SYS_COMMON_OPERATION_FAIL, "链接不在系统解析范围");
        }
        try {
            JSONObject analysisUrl = spiderApi.fastCrawlArticle(dto.getWeb_url(), articleType, "");
            if ("200".equals(analysisUrl.getString("status"))) {
                JSONArray jsonArray = analysisUrl.getJSONArray("data");
                if (jsonArray != null && jsonArray.size() > 0) {
                    JSONObject article = jsonArray.getJSONObject(0);
                    if ("今日头条".equals(article.get("domain_name"))) {
                        article.put("domain_name", SourceTypeMenu.TOU_TIAO.getValue());
                    } else if ("新浪微博".equals(article.get("domain_name"))) {
                        article.put("domain_name", SourceTypeMenu.WEI_BO.getValue());
                    }
                    return Result.successResult().setData(article);
                }

            }
            if (analysisUrl != null) {
                resultMap = analysisUrl;
                return Result.fail(StatusCode.SYS_COMMON_OPERATION_FAIL, "解析失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        resultMap.put("success", false);

        return Result.fail(StatusCode.SYS_COMMON_OPERATION_FAIL, "解析失败");
    }
}
