package com.izhonghong.ubc.information.controller;


import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izhonghong.ubc.information.aop.OperateLog;
import com.izhonghong.ubc.information.constant.BusinessConstant;
import com.izhonghong.ubc.information.entity.OperationLog;
import com.izhonghong.ubc.information.entity.dto.OperationLogDTO;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.service.OperationLogService;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @since 2022-05-16
 */
@RestController
@RequestMapping("/operationLog")
public class OperationLogController {
	
	@Autowired
    private OperationLogService operationLogService;
	
	@ApiOperation(value = "日志列表的分页查询", notes = "日志列表的分页查询")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的分页查询")
	@PostMapping("selectPage")
	 public Result selectPage(@RequestBody OperationLogDTO logDto){
			
			QueryWrapper<OperationLog> query = new QueryWrapper<OperationLog>();
			query.lambda().eq(!StringUtils.isEmpty(logDto.getOperateModule()), OperationLog::getOperateModule, logDto.getOperateModule())
			.ge(!StringUtils.isEmpty(logDto.getStartTime()), OperationLog::getCreatedAt, logDto.getStartTime())
			.le(!StringUtils.isEmpty(logDto.getEndTime()), OperationLog::getCreatedAt, logDto.getEndTime());
			
			IPage<OperationLog> page = operationLogService.page(new Page<>(logDto.getPageNo(),logDto.getPageSize()),query);
			return Result.successResult().setData(page);
	 	
	 }

	@ApiOperation(value = "日志列表的分页查询", notes = "日志列表的分页查询")
	@OperateLog(module = BusinessConstant.OPERATE_MODULE_WE_MEDIA, operateType = BusinessConstant.OPERATE_TYPE_SELECT, operateDescription = "媒体列表的分页查询")
	@PostMapping("selectLogByUid")
	 public Result selectLogByUid(@RequestBody OperationLogDTO logDto){
		    List<OperationLog> operationLogList = new ArrayList<OperationLog>();
			if(StringUtils.isEmpty(logDto.getUuid())) {
				return Result.successResult().setData(operationLogList);
			}
			
			QueryWrapper<OperationLog> query = new QueryWrapper<OperationLog>();
			query.lambda().eq(OperationLog::getUuid, logDto.getUuid()).orderByDesc(OperationLog::getCreatedAt);
			operationLogList = operationLogService.list(query);
			
			return Result.successResult().setData(operationLogList);
	 	
	 }
}
