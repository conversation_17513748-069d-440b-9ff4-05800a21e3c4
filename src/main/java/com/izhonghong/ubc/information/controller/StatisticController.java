package com.izhonghong.ubc.information.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.izhonghong.ubc.information.entity.ImportantAccountOrgRelation;
import com.izhonghong.ubc.information.entity.ImportantSiteOrgRelation;
import com.izhonghong.ubc.information.entity.vo.AccountSiteStatistcVo;
import com.izhonghong.ubc.information.result.Result;
import com.izhonghong.ubc.information.service.ImportantAccountOrgRelationService;
import com.izhonghong.ubc.information.service.ImportantSiteOrgRelationService;
import com.izhonghong.ubc.information.util.DateUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2023-07-17 10:07
 * @menu
 */
@Api(tags = "统计接口")
@RestController
@RequestMapping("statistc")
public class StatisticController {


    @Autowired
    private ImportantAccountOrgRelationService importantAccountOrgRelationService;

    @Autowired
    private ImportantSiteOrgRelationService importantSiteOrgRelationService;


    @GetMapping("acctounAndSite")
    public Result<AccountSiteStatistcVo> acctounAndSite(@RequestParam(value = "orgId") Integer orgId,
                                                        @RequestParam(value = "beginDate") String beginDateStr,
                                                        @RequestParam(value = "endDate") String endDateStr) {
        LocalDateTime beginDate=DateUtil.parseLocalDateTime(beginDateStr+" 00:00:00");
        LocalDateTime endDate=DateUtil.parseLocalDateTime(endDateStr+" 23:59:59");
        LambdaQueryWrapper<ImportantAccountOrgRelation> accountOrgQueryWrapper = new LambdaQueryWrapper<>();
        accountOrgQueryWrapper.eq(ImportantAccountOrgRelation::getOrgId, orgId);
        accountOrgQueryWrapper.between(ImportantAccountOrgRelation::getCreateTime,beginDate,endDate );
        Integer accountTotal = importantAccountOrgRelationService.count(accountOrgQueryWrapper);
        LambdaQueryWrapper<ImportantSiteOrgRelation> siteOrgQueryWrapper = new LambdaQueryWrapper<>();
        siteOrgQueryWrapper.eq(ImportantSiteOrgRelation::getOrgId, orgId);
        siteOrgQueryWrapper.between(ImportantSiteOrgRelation::getCreateTime,beginDate,endDate );
        Integer siteTotal=importantSiteOrgRelationService.count(siteOrgQueryWrapper);
        AccountSiteStatistcVo data=new AccountSiteStatistcVo();
        data.setAccountTotal(accountTotal);
        data.setSiteTotal(siteTotal);
        return Result.<AccountSiteStatistcVo>successResult().setData(data);
    }


}
