package com.izhonghong.ubc.information.constant;

public class CommonConstant {

    public static final String SYS_PREFIX = "information_";
    
    /**
     * 映射词库在增改删就存储带redis中，key格式为 lexicon_mapping_{orgid}_{lexicon_id}
     * ***/
    public static final String LEXICON_REDIS_KEY_PREFIX = "lexicon_mapping_%s_%s";

    public static final String CHARSET_UTF8 = "utf-8";

    public static final String FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_DATE = "yyyy-MM-dd";

    public static final String SUCCESS_CODE = "0";

    public static final String USER_DIR = System.getProperty("user.dir");
    
    public static final String LEXICON_SIGN = "$";
    
    /**
     * 手机账号加密的公用秘钥
     * **/
    public static final String AES_DECRYPT_KEY = "9ca3caf1c05e4408a1c0c48b5b2b8edf";

    /**
     * 管理员
     * **/
    public static final Integer PATROLER_SYSTEM_ROLE_ADMIN = 1;

    /**
     * 内服
     * **/
    public static final Integer PATROLER_SYSTEM_ROLE_NEIFU = 5;

    /**
     * 客户
     * **/
    public static final Integer PATROLER_SYSTEM_ROLE_CUSTOMER = 6;

    /**
     * 词库名称长度
     */
    public static final int LEXICON_NAME_MAX_LENGTH = 30;

    /**
     * 客户词库关键词长度
     */
    public static final int LEXICON_KEYWORD_MAX_LENGTH = 3000;

    /**
     * 系统词库关键词长度
     */
    public static final int LEXICON_KEYWORD_MAX_LENGTH2 =5000;

    /**
     * 默认翻页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;
    
    
    /*****词库类型*****/
    //关键词匹配类型
    public static final Integer  ERROR_TYPE_OF_KEYWORD = 1;
    
    //错别字类型
    public static final Integer ERROR_TYPE_OF_TYPO = 2;
    
    //网易文本纠错
    public static final Integer ERROR_TYPE_OF_NETEASE_GRAMMARFIX = 3;
    
    //网易文本检测
    public static final Integer ERROR_TYPE_OF_NETEASE_TEXT_CHECK = 4;
    
    //错误内容
    public static final String ERROR_COTENT = "%s(共计%s处)";
    
    //批量获取快照的
    public static final String PATROLER_BATCH_SNAPSHOT_REDIS_KEY = "information_batch_snapshot_redis_key";

    public static final String INLAND_AREA_TREE_KEY = "information_inland_area_tree_key";

    public static final String MEDIA_TYPE_TREE_KEY = "information_media_type_tree_key";
    public static final String MEDIA_TYPE_TREE_KEY_NEW = "information_media_type_tree_key_new";

}
