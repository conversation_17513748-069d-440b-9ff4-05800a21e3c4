package com.izhonghong.ubc.information.constant;

/**
 * 类描述:
 *
 * <AUTHOR>
 * @date 2023-07-29 17:40:57
 */
public enum PlatformTypeEnum {

    WEIBO(1, "微博", "101"),
    WEIXIN(0, "微信", "102"),
    TOUTIAO(10, "头条", ""),
    QIE(11, "企鹅号", "103:02:04"),
    BAIJIA(12, "百家号", "103:07"),
    DAFENG(16, "大风", "103:02:04"),
    YIDIAN(17, "一点", "105:01:04"),
    DOUYING(20382, "抖音", "107:02"),
    XIGUA(21727, "西瓜", "107:02"),
    BILIBILI(20410, "哔哩哔哩", "107:01");

    private int type;

    private String name;

    private String code;


    PlatformTypeEnum(int type, String name, String code) {
        this.type = type;
        this.name = name;
        this.code = code;
    }

}
