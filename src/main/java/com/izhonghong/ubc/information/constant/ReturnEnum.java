package com.izhonghong.ubc.information.constant;

public enum  ReturnEnum {

    //基础返回
    FAIL("-1","操作失败"),
    OK("0", "操作成功"),


    //专题返回
    SUBJECT_DATA_EXIST("701","该分类下存在专题不可删除"),
    GROUP_NAME_EXIST("702","该分组名称已存在"),

    //微信相关
    LOGIN_CODE_REQUIRED("801","微信扫码登录必须携带code"),
    LOGIN_CODE_INVALID("802","微信扫码登录code非法"),
    OPENID_REQUIRED("803","微信扫码登录必须携带openid"),
    REFRESH_TOKEN_FAILED("804","刷新token失败"),
    LINK_IS_PERMANENT("805","该链接是永久链接"),
    PERMANENT_LINK_SAVE_FAILED("806","永久链接保存失败"),
    ;
    public String code;
    public String msg;

    ReturnEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}