package com.izhonghong.ubc.information.constant;

import org.apache.commons.lang3.StringUtils;

/***
 * 文章媒体类型菜单
 * baiqy
 * **/
public enum ArticleTypeEnum {

    SINA("0", "微博", "101"),
    NEWS_SITE("1", "网站", "103"),
    WE_CHAT("2", "微信", "102"),
    BBS("3", "互动论坛", "104"),
    BLOG("4", "互动论坛", "104"),
    NEWS_PAPER("5", "数字报", "106"),
    VIDEO("6", "视频", "107"),
    QQ("7", "微信", "102"),
    POSTER("8", "互动论坛", "104"),
    ABROAD("9", "境外", "108"),
    TWITTER("10", "境外", "108"),
    NEWS_CLIENT("11", "客户端", "105"),
    TELEGRAPH("15", "境外", "108"),
    YOUTUBE("16", "境外", "108"),
    ;
    private String articleType;
    private String articleTypeValue;
    /**
     * 新媒体类型code
     */
    private String mediaCode;

    public String getMediaCode() {
        return mediaCode;
    }

    public String getArticleType() {
        return articleType;
    }

    public String getArticleTypeValue() {
        return articleTypeValue;
    }

    ArticleTypeEnum(String articleType, String articleTypeValue, String mediaCode) {
        this.articleType = articleType;
        this.articleTypeValue = articleTypeValue;
        this.mediaCode = mediaCode;
    }

    public static String getArticleTypeValueByMediaCode(String mediaCode) {
        if (StringUtils.isNotBlank(mediaCode)) {
            for (ArticleTypeEnum value : values()) {
                if (mediaCode.equals(value.getMediaCode())) {
                    return value.articleTypeValue;
                }
            }
        }
        return null;
    }

}
