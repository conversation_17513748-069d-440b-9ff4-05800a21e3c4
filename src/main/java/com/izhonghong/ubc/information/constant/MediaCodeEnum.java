package com.izhonghong.ubc.information.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024-02-03
 */
public enum MediaCodeEnum {

    WB_1(1, "微博", "101"),
    WB_1_1(2, "微博蓝V号", "101:01"),
    WB_1_1_1(3, "微博政务号", "101:01:01"),
    WB_1_1_1_1(4, "央级微博政务号", "101:01:01:01"),
    WB_1_1_1_2(4, "省级微博政务号", "101:01:01:02"),
    WB_1_1_1_3(4, "市级微博政务号", "101:01:01:03"),
    WB_1_1_1_4(4, "其他微博政务号", "101:01:01:04"),
    WB_1_1_2(3, "微博媒体号", "101:01:02"),
    WB_1_1_2_1(4, "央级微博媒体号", "101:01:02:01"),
    WB_1_1_2_2(4, "省级微博媒体号", "101:01:02:02"),
    WB_1_1_2_3(4, "市级微博媒体号", "101:01:02:03"),
    WB_1_1_2_4(4, "商业微博媒体号", "101:01:02:04"),
    WB_1_1_2_5(4, "其他微博媒体号", "101:01:02:05"),
    WB_1_1_3(3, "微博企业号", "101:01:03"),
    WB_1_1_4(3, "微博机构号", "101:01:04"),
    WB_1_2(2, "微博金V号", "101:02"),
    WB_1_3(2, "微博橙V号", "101:03"),
    WB_1_4(2, "微博个人号", "101:04"),
    WX_1(1, "微信", "102"),
    WX_1_1(2, "微信政务号", "102:01"),
    WX_1_1_1(3, "央级政务公众号", "102:01:01"),
    WX_1_1_2(3, "省级政务公众号", "102:01:02"),
    WX_1_1_3(3, "市级政务公众号", "102:01:03"),
    WX_1_1_4(3, "其他政务公众号", "102:01:04"),
    WX_1_2(2, "微信媒体号", "102:02"),
    WX_1_2_1(3, "央级媒体公众号", "102:02:01"),
    WX_1_2_2(3, "省级媒体公众号", "102:02:02"),
    WX_1_2_3(3, "市级媒体公众号", "102:02:03"),
    WX_1_2_4(3, "商业媒体公众号", "102:02:04"),
    WX_1_2_5(3, "其他媒体公众号", "102:02:05"),
    WX_1_3(2, "微信机构号", "102:03"),
    WX_1_4(2, "微信企业号", "102:04"),
    WX_1_5(2, "微信个人号", "102:05"),
    WZ_1(1, "网站", "103"),
    WZ_1_1(2, "政务网站", "103:01"),
    WZ_1_1_1(3, "央级政务网站", "103:01:01"),
    WZ_1_1_2(3, "省级政务网站", "103:01:02"),
    WZ_1_1_3(3, "市级政务网站", "103:01:03"),
    WZ_1_1_4(3, "其他政务网站", "103:01:04"),
    WZ_1_2(2, "新闻网站", "103:02"),
    WZ_1_2_1(3, "央级新闻网站", "103:02:01"),
    WZ_1_2_2(3, "省级新闻网站", "103:02:02"),
    WZ_1_2_3(3, "市级新闻网站", "103:02:03"),
    WZ_1_2_4(3, "其他新闻网站", "103:02:04"),
    WZ_1_3(2, "行业网站", "103:03"),
    WZ_1_4(2, "资讯平台", "103:04"),
    WZ_1_5(2, "企业官网", "103:05"),
    WZ_1_6(2, "机构组织", "103:06"),
    WZ_1_7(2, "其他网站", "103:07"),
    LT_1(1, "互动论坛", "104"),
    LT_1_1(2, "论坛", "104:01"),
    LT_1_1_1(3, "央级媒体论坛", "104:01:01"),
    LT_1_1_2(3, "省级媒体论坛", "104:01:02"),
    LT_1_1_3(3, "市级媒体论坛", "104:01:03"),
    LT_1_1_4(3, "其他媒体论坛", "104:01:04"),
    LT_1_2(2, "问答", "104:02"),
    LT_1_3(2, "咨询投诉", "104:03"),
    LT_1_3_1(3, "央级咨询投诉", "104:03:01"),
    LT_1_3_2(3, "省级咨询投诉", "104:03:02"),
    LT_1_3_3(3, "市级咨询投诉", "104:03:03"),
    LT_1_3_4(3, "其他咨询投诉", "104:03:04"),
    LT_1_4(2, "贴吧", "104:04"),
    LT_1_5(2, "博客", "104:05"),
    KHD_1(1, "客户端", "105"),
    KHD_1_1(2, "APP", "105:01"),
    KHD_1_1_1(3, "新闻APP", "105:01:01"),
    KHD_1_1_1_1(4, "央级新闻APP", "105:01:01:01"),
    KHD_1_1_1_2(4, "省级新闻APP", "105:01:01:02"),
    KHD_1_1_1_3(4, "市级新闻APP", "105:01:01:03"),
    KHD_1_1_1_4(4, "其他新闻APP", "105:01:01:04"),
    KHD_1_1_2(3, "政务APP", "105:01:02"),
    KHD_1_1_2_1(4, "央级政务APP", "105:01:02:01"),
    KHD_1_1_2_2(4, "省级政务APP", "105:01:02:02"),
    KHD_1_1_2_3(4, "市级政务APP", "105:01:02:03"),
    KHD_1_1_2_4(4, "其他政务APP", "105:01:02:04"),
    KHD_1_2(2, "自媒体", "105:02"),
    KHD_1_2_1(3, "新闻自媒体号", "105:02:01"),
    KHD_1_2_1_1(4, "央级新闻自媒体号", "105:02:01:01"),
    KHD_1_2_1_2(4, "省级新闻自媒体号", "105:02:01:02"),
    KHD_1_2_1_3(4, "市级新闻自媒体号", "105:02:01:03"),
    KHD_1_2_1_4(4, "其他新闻自媒体号", "105:02:01:04"),
    KHD_1_2_2(3, "政务自媒体号", "105:02:02"),
    KHD_1_2_2_1(4, "央级政务自媒体号", "105:02:02:01"),
    KHD_1_2_2_2(4, "省级政务自媒体号", "105:02:02:02"),
    KHD_1_2_2_3(4, "市级政务自媒体号", "105:02:02:03"),
    KHD_1_2_2_4(4, "其他政务自媒体号", "105:02:02:04"),
    KHD_1_2_3(3, "行业自媒体号", "105:02:03"),
    KHD_1_2_4(3, "其他自媒体号", "105:02:04"),
    SZB_1(1, "数字报", "106"),
    SZB_1_1(2, "央级数字报", "106:01"),
    SZB_1_2(2, "省级数字报", "106:02"),
    SZB_1_3(2, "市级数字报", "106:03"),
    SZB_1_4(2, "其他数字报", "106:04"),
    SP_1(1, "视频", "107"),
    SP_1_1(2, "视频平台", "107:01"),
    SP_1_2(2, "短视频", "107:02"),
    SP_1_3(2, "电视视频", "107:03"),
    SP_1_3_1(3, "央级电视视频", "107:03:01"),
    SP_1_3_2(3, "省级电视视频", "107:03:02"),
    SP_1_3_3(3, "市级电视视频", "107:03:03"),
    SP_1_3_4(3, "其他电视视频", "107:03:04"),
    JW_1(1, "境外", "108"),
    JW_1_1(2, "推特", "108:01"),
    JW_1_2(2, "脸书", "108:02"),
    JW_1_3(2, "油管", "108:03"),
    JW_1_4(2, "境外网站", "108:04"),
    JW_1_5(2, "境外数字报", "108:05"),
    JW_1_6(2, "境外视频", "108:06"),
    JW_1_7(2, "境外论坛", "108:07"),
    JW_1_8(2, "境外博客", "108:08"),
    JW_1_9(2, "境外app", "108:09"),
    JW_1_10(2, "境外自媒体号", "108:010");

    private Integer level;

    private String name;

    private String code;

    MediaCodeEnum(int level, String name, String code) {
        this.level = level;
        this.name = name;
        this.code = code;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (MediaCodeEnum value : MediaCodeEnum.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
        }
        return null;
    }

}
