package com.izhonghong.ubc.information.aop;

import com.alibaba.fastjson.JSON;
import com.izhonghong.ubc.information.dao.OperateRecordDao;
import com.izhonghong.ubc.information.entity.OperateRecord;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.service.personal.UserService;
import com.izhonghong.ubc.information.service.system.OperateRecordService;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Component
@Aspect
@Slf4j
public class OperateLogAspect {

    /**
     * 2020.10.26 使用说明
     * 在需要记录操作的方法上添加注解OperateLog
     * 此注解需加三个形参,module方法所属模块/operateType操作类型/operateDescription操作描述
     * module和operateType参数均在常量类里定义并与数据库一致,不可使用魔数魔文,如数据库添加新值则需一并更新常量类
     */

    @Autowired
    protected UserService userService;

    @Autowired
    private OperateRecordService operateRecordService;
    @Autowired
    private OperateRecordDao operateRecordDao;

    private User user;
    private HttpServletRequest request = null;
    private HttpServletResponse response = null;
    private long startTimeMillis = 0; // 开始时间


    @Before(value = "@annotation(com.izhonghong.ubc.information.aop.OperateLog)")
    public void before(){
        startTimeMillis = System.currentTimeMillis();
    }

    @After(value = "@annotation(com.izhonghong.ubc.information.aop.OperateLog)")
    public void saveOperate(JoinPoint joinPoint){
        //获取请求和响应
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes)ra;
        response = sra.getResponse();
        request = sra.getRequest();

        //获取类名和方法名
        String targetName = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();

        Class<?> targetClass = null;
        try {
            targetClass = Class.forName(targetName);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("找不到目标类,原因是"+e.getMessage());
        }

        if(targetClass != null) {

        	//获取方法列表
	        Method[] methods = targetClass.getMethods();

	        //操作记录实体字段
	        String module,operateType,description,operateTime,reason = "";
	        boolean operateResult;

	        Class<?>[] clazzs;
	        Object[] arguments = joinPoint.getArgs();
	        for (Method method : methods) {
	            if (method.getName().equals(methodName)) {//命中目标方法
	                clazzs = method.getParameterTypes();
	                if (clazzs!=null&&clazzs.length == arguments.length
	                        &&method.getAnnotation(OperateLog.class)!=null) {
	                    //获取相关操作数据
	                    user = (User) request.getSession().getAttribute("loginUserInfo");
						try {
							if (user == null) {
								user = userService.getLoginUser(request);
							}
						} catch (Exception e) {
							e.printStackTrace();
							break;
						}
						Map<String,String> paramMap = converMap(request.getParameterNames());

	                    String params = "";
	                    try {
	                    if(paramMap.isEmpty() && !"GET".equals(request.getMethod())) {
	                    	if(arguments != null && arguments.length > 0) {
		                    	Object obj = arguments[0];
		                    	if (obj instanceof ServletRequest || obj instanceof ServletResponse || obj instanceof MultipartFile) {
		                            log.info("OperateLogAspect args ==> {}",obj);
		                        }else if(obj != null) {
			                    	params = JSON.toJSONString(obj);
			                    }
	                    	}
	                    }else {
	                    	params = JSON.toJSONString(paramMap);
	                    }
	                    }catch (Exception e) {
							log.error("OperateLogAspect error.{}",e);
						}
	                    module = method.getAnnotation(OperateLog.class).module();
	                    operateType = method.getAnnotation(OperateLog.class).operateType();
	                    description = method.getAnnotation(OperateLog.class).operateDescription();
	                    operateResult = response.getStatus() == 200;
	                    operateTime = (System.currentTimeMillis() - startTimeMillis) + "ms";
    					try {

	                    	String actualIpAddr  = request.getHeader("x-forwarded-for");

	                    	if (actualIpAddr == null || actualIpAddr.length() == 0 || "unknown".equalsIgnoreCase(actualIpAddr) || "127.0.0.1".equalsIgnoreCase(actualIpAddr)) {
	                			actualIpAddr = request.getHeader("Proxy-xff");
	                		}
	                		if (actualIpAddr == null || actualIpAddr.length() == 0 || "unknown".equalsIgnoreCase(actualIpAddr) || "127.0.0.1".equalsIgnoreCase(actualIpAddr)) {
	                			actualIpAddr = request.getHeader("Proxy-Client-IP");
	                		}
	                		if (actualIpAddr == null || actualIpAddr.length() == 0 || "unknown".equalsIgnoreCase(actualIpAddr)|| "127.0.0.1".equalsIgnoreCase(actualIpAddr)) {
	                			actualIpAddr = request.getHeader("WL-Proxy-Client-IP");
	                		}
	                		if (actualIpAddr == null || actualIpAddr.length() == 0 || "unknown".equalsIgnoreCase(actualIpAddr)|| "127.0.0.1".equalsIgnoreCase(actualIpAddr)) {
	                			actualIpAddr = request.getRemoteAddr();
	                		}
	                		log.info("OperateLogAspect actualIpAddr =========> " + actualIpAddr);

		                    //用同名构造函数构建实体并入库
		                    OperateRecord operateRecord = new OperateRecord(module,user.getId(),user.getOrgid(), user.getLoginName(), user.getType(),
		                            Integer.valueOf(operateType),operateResult,reason,actualIpAddr,description,operateTime,params);
		                    operateRecordDao.saveOperateRecord(operateRecord);
	                    }catch(Exception e) {
	                    	e.printStackTrace();
	                    	log.info("OperateLogAspect saveOperateRecord error.{}",e);
	                    }
	                    break;
	                }
	            }
	        }

        }
    }

    /**
     * 转换request 请求参数
     *
     * @param paramMap request获取的参数数组
     */
    public Map<String, String> converMap(Enumeration<String> enu) {
        Map<String, String> rtnMap = new HashMap<String, String>();
        while(enu.hasMoreElements()){

            String paraName=(String)enu.nextElement();
            rtnMap.put(paraName, request.getParameter(paraName));

        }
        return rtnMap;
    }





}
