package com.izhonghong.ubc.information.aop;

import java.sql.SQLException;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.izhonghong.ubc.inf.core.constant.ErrorCodeEnum;
import com.izhonghong.ubc.inf.core.protocol.IzhResponseBody;
import com.izhonghong.ubc.information.exception.ApplicationException;
import com.izhonghong.ubc.information.util.ResponseUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * 功能描述: AOP异常处理
 * <AUTHOR>
 * @date 2019-06-04 16:10:34
 */
@RestControllerAdvice
@Slf4j
public class BaseRestControllerAdvice {

    private static final String ERR_CODE = "1010500";

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler({NullPointerException.class})
    public IzhResponseBody handleNullPointerException(Exception e, HttpServletRequest request) {
        e.printStackTrace();
        log.error("空指针异常", e);
        return ResponseUtil.error(ErrorCodeEnum.NOT_RIGHT);
    }


    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler({SQLException.class})
    public IzhResponseBody handleSqlException(Exception e, HttpServletRequest request) {
        e.printStackTrace();
        log.error("sql异常", e);
        return ResponseUtil.error(ERR_CODE, "sql异常，请检查参数是否正确");
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(ApplicationException.class)
    public IzhResponseBody serviceErrorHandler(HttpServletRequest request, ApplicationException e) {
        e.printStackTrace();
        log.error("业务异常", e);
    	return ResponseUtil.error(ERR_CODE, e.getMessage());
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler({Exception.class})
    public IzhResponseBody handle500Exception(Exception e, HttpServletRequest request) {
        String message = Optional.ofNullable(e.getMessage()).orElse(ErrorCodeEnum.UNHANDLED_EXCEPTION.getErrorMessage());
        if (message.contains("Exception")) {
            if (message.contains("sql") || message.contains("SQL")) {
                message = "sql异常，请检查参数是否正确";
            } else {
                message = ErrorCodeEnum.UNHANDLED_EXCEPTION.getErrorMessage();
            }
        }
        log.error("系统异常", e);
        return ResponseUtil.error(ERR_CODE, message);
    }

}
