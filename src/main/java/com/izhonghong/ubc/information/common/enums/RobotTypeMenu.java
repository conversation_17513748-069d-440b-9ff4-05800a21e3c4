package com.izhonghong.ubc.information.common.enums;

/***
 * 机器人类型枚举
 * <AUTHOR>
 * @date 2021-12-31
 * **/
public enum RobotTypeMenu {

	WE_CHAT(1,"微信","weChatMessageSendServiceImpl"),
	EE_WE_CHAT(2,"企业微信","enterpriseWeChatMessageSendServiceImpl");
	
	private Integer code;
	
	private String value;
	
	private String service;
	
	
	public Integer getCode() {
		return code;
	}


	public void setCode(Integer code) {
		this.code = code;
	}


	public String getValue() {
		return value;
	}


	public String getService() {
		return service;
	}


	public void setService(String service) {
		this.service = service;
	}


	public void setValue(String value) {
		this.value = value;
	}


	RobotTypeMenu(Integer code ,String value,String service) {
		this.code = code;
		this.value = value;
		this.service = service;
	}
}
