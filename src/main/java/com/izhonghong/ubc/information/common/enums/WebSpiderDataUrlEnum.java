package com.izhonghong.ubc.information.common.enums;

/**
 * baiqy
 *
 ***/
public enum WebSpiderDataUrlEnum {

	ES_SEARCH("/traditional/users/query","es的查询接口"),
	ES_INSERT("/traditional/users/add","新增接口"),
	ES_UPDATE("/traditional/users/update","修改接口"),
	ES_DELETE("/traditional/users/delete","删除接口"),
	ES_MEDIA_TYPE("/traditional/information/source","媒体类型树"),
	ES_SEARCH_MID("","根据id查询"),
	ES_WE_MEDIA_INSERT_OR_UPDATE("/selfMedia/userInfo/adding/duplication","自媒体的新增or修改"),
	ES_WE_MEDIA_DELETE("/selfMedia/userInfo/delete","删除自媒体"),
	ES_SEARCH_INFORMATION("/user/information?match=1&sourceType=%s&startRow=%s&size=%s","自媒体账号查询"),
	ES_ALL_WE_MEDIA_PLATFORM_TYPE("/userRegionalization/mediaTagAll","全部的自媒体平台类型"),
	ES_ORGANIZATION_SEARCH("/traditional/organizer/suggestions?keyword=", "获取主办单位");

	private String url;

	private String describe;

	public String getUrl() {
		return url;
	}

	public String getDescribe() {
		return describe;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	WebSpiderDataUrlEnum(String url, String describe) {
		this.url = url;
		this.describe = describe;
	}


}
