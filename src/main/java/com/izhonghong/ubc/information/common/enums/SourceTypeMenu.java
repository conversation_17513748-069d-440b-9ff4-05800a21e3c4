package com.izhonghong.ubc.information.common.enums;

/**
 * <AUTHOR>
 * 信息源的媒体类型枚举
 * **/
public enum SourceTypeMenu {
	
	WE_CHAT("2","微信"),
	WEI_BO("3","微博"),
	TOU_TIAO("4","头条");
	
	private String code;
	
	private String value;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	SourceTypeMenu(String code, String value) {
		this.code = code;
		this.value = value;
	}
	
	public static boolean checkType(String name) {
		boolean flage = false;
		for (SourceTypeMenu emun : values()) {
			if (emun.getValue().equals(name)) {
				flage = true;
				break;
			}
		}
		return flage;
	}

	
}
