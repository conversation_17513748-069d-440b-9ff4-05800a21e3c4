package com.izhonghong.ubc.information.common.enums;

/**
 * baiqy
 *
 ***/
public enum WebSpiderNewDataUrlEnum {

	ES_SEARCH("/newMedia/search/information/source/","es的查询接口"),
	ES_INSERT("/newMedia/add/information/source/","新增接口"),
	ES_UPDATE("/newMedia/update/information/source/","修改接口"),
	ES_DELETE("/newMedia/delete/information/source/","删除接口"),
	ES_MEDIA_TYPE("/newMedia/get_new/information/source/","媒体类型树"),
	ES_SEARCH_MID("/newMedia/get/information?k3_id=","根据id查询/获取单个信息源详情"),
	ES_WE_MEDIA_INSERT_OR_UPDATE("/newMediaUser/upsertMediaUsers/","自媒体的新增or修改"),
	ES_WE_MEDIA_DELETE("/newMediaUser/deleteMediaUsers/","删除自媒体"),
	ES_SEARCH_INFORMATION("/newMediaUser/commonQuery/","自媒体账号查询"),
	ES_ALL_WE_MEDIA_PLATFORM_TYPE("/userRegionalization/mediaTagAll","全部的自媒体平台类型"),
	ES_ORGANIZATION_SEARCH("/newMedia/get/organizer/list?keyword=", "根据关键词获取主办单位");

	private String url;

	private String describe;

	public String getUrl() {
		return url;
	}

	public String getDescribe() {
		return describe;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	WebSpiderNewDataUrlEnum(String url, String describe) {
		this.url = url;
		this.describe = describe;
	}


}
