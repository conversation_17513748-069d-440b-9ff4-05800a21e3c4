package com.izhonghong.ubc.information.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * **/
public enum SourceStatusEnum {

	STOP(-1,"已停止"),
	IMPLEMENTED(0,"待实施"),
	HANDING(1,"采集中");
	private Integer code;
	
	private String value;

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getValue() {
		return value;
	}

	public static Map<Integer,String> getMap(){
		Map<Integer,String> reMap = new HashMap<Integer, String>();
		for (SourceStatusEnum enums : values()) {
			reMap.put(enums.getCode(), enums.getValue());
		}
		return reMap;
	}
	
	SourceStatusEnum(Integer code,String value) {
		this.value = value;
		this.code = code;
	}
	
}
