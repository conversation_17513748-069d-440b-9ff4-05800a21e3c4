package com.izhonghong.ubc.information.common.enums;

/**
 * 系统预警类型
 * <AUTHOR>
 * **/
public enum SystemAlertTypeMenu {
	
	SYSTEM_ALL(0,"全系统的"),
	EE_WE_CHAT(1,"企业微信"),
	MESSAGE(2,"短信"),
	EMAIL(3,"邮箱"),
	WE_CHAT(4,"微信");
	
	
	private Integer code;
	private String mean;

	SystemAlertTypeMenu(Integer code,String mean){
		this.code = code;
		this.mean = mean;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMean() {
		return mean;
	}

	public void setMean(String mean) {
		this.mean = mean;
	}
	
	
}
