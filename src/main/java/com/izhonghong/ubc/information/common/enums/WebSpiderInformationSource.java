package com.izhonghong.ubc.information.common.enums;

import com.izhonghong.ubc.information.service.WebSpiderInformationSourceDataService;
import com.izhonghong.ubc.information.service.abstracts.AbstractsInformationSourceDataService;
import com.izhonghong.ubc.information.service.impl.abstracts.WebSpiderInformationSourceDataServiceImpl;
/**
 * 单例模式
 * <AUTHOR>
 * **/
public enum WebSpiderInformationSource {

	 INSTANCE;
	
	 private AbstractsInformationSourceDataService abstractsInformationSourceDataService;
	 
	 private WebSpiderInformationSourceDataService webSpiderInformationSourceDataService;
	 
	 WebSpiderInformationSource(){
		 this.webSpiderInformationSourceDataService = new WebSpiderInformationSourceDataServiceImpl();
	 }
	 
	 public AbstractsInformationSourceDataService getInstance(Integer type) {
		 if(SourceDataTypeEnum.WEB_SPIDER.getCode().equals(type)) {
			 return this.webSpiderInformationSourceDataService;
		 }
		return abstractsInformationSourceDataService;
		 
	 }
}
