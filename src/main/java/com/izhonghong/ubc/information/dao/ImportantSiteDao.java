package com.izhonghong.ubc.information.dao;

import com.izhonghong.ubc.information.entity.ImportantSite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface ImportantSiteDao extends BaseMapper<ImportantSite> {

    public int batchInsertImportantSite(@Param("importantSites") List<ImportantSite> importantSites);

}
