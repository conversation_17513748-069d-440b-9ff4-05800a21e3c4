package com.izhonghong.ubc.information.dao;

import com.izhonghong.ubc.information.entity.ImportantAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface ImportantAccountDao extends BaseMapper<ImportantAccount> {

    int batchInsertImportantAccount(@Param("importantAccounts") List<ImportantAccount> importantAccounts);

}
