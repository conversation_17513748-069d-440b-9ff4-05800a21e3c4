package com.izhonghong.ubc.information.service.impl;

import com.izhonghong.ubc.information.entity.OperationLog;
import com.izhonghong.ubc.information.constant.DataSourceConstant;
import com.izhonghong.ubc.information.dao.OperationLogDao;
import com.izhonghong.ubc.information.service.OperationLogService;
import com.izhonghong.ubc.information.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <AUTHOR>
 * @since 2022-05-16
 */
@Service
@DS(DataSourceConstant.DATA_SOURCE)
public class OperationLogServiceImpl extends ServiceImpl<OperationLogDao, OperationLog> implements OperationLogService {

	@Override
	public OperationLog selectOperationLogByUid(String uid) {
		if(StringUtils.isEmpty(uid)) {
			return null;
		}
		QueryWrapper<OperationLog> queryWrapper = new QueryWrapper<OperationLog>();
		queryWrapper.lambda().eq(OperationLog::getUuid, uid).orderByDesc(OperationLog::getCreatedAt);
		List<OperationLog> list = this.list(queryWrapper);
		if(!StringUtil.isListNull(list)) {
			return list.get(0);
		}
		return null;
	}

}
