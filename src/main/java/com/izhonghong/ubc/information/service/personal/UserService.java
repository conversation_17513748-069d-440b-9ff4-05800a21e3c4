package com.izhonghong.ubc.information.service.personal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.personal.Org;
import com.izhonghong.ubc.information.entity.personal.User;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface UserService {

    User getLoginUser(HttpServletRequest request);


    JSONArray getUserListByUms(String token, int type, Integer orgId);

    JSONObject updateUserInfoByUms(String jsonStr, String token);

    JSONObject getLoginUserUms(HttpServletRequest request);

    List getOrgListByUms(String token);

    Org getOrgInforByOrgId(String token,String orgId);

	public JSONObject getLoginUserForCas(HttpServletRequest request);

	List getUserInforByIds(List<Integer> userids, String token);

	JSONObject getLoginUerInformation(String token);

	/**
     *根据用户名查询
     * **/
	List<User> getUerInformationByName(String string, String token);

	/**
	 * 获取角色信息
	 * **/
	Map<String, Object> findRole(String loginName, int orgid, String token);

	Map<Integer, Object> getLoginUserForCasByRoleId(String token,List<Long> roleIds);

	Map<Integer, String> convertBusinessIdNameMap(String ids, int type);

	String convertNames(Map<Integer, String> idNameMap, String ids);
}
