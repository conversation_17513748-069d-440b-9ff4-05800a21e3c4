package com.izhonghong.ubc.information.service.abstracts;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.RobotInforSet;

public abstract class AbstractsRobotMssageSendService {
	
	/***
	 * 发送文本接口
	 * **/
	public abstract JSONObject sendTextMessage(RobotInforSet robotInforSet) throws Exception;
	
	
	/***
	 * 发送图片接口
	 * **/
	public abstract void sendImageMessage(RobotInforSet robotInforSet) throws Exception;
	
	
	/***
	 * 发送文件接口
	 * **/
	public abstract void sendFileMessage(RobotInforSet robotInforSet) throws Exception;
	

}
