package com.izhonghong.ubc.information.service.impl.abstracts;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.RobotInforSet;
import com.izhonghong.ubc.information.service.EnterpriseWechatRobotInforSet;
import com.izhonghong.ubc.information.service.abstracts.EnterpriseWeChatMessageSendService;
import com.izhonghong.ubc.information.util.components.EnterpriseWeChatMessageSendUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class EnterpriseWeChatMessageSendServiceImpl extends EnterpriseWeChatMessageSendService{

	@Override
	public JSONObject sendTextMessage(RobotInforSet robotInforSet) throws Exception {
		 EnterpriseWechatRobotInforSet robotInfor = (EnterpriseWechatRobotInforSet) robotInforSet;
		 log.info("sendTextMessage === webHook ={}  content = {}",robotInfor.getWebHook(),robotInfor.getText());
    	
		 if(StringUtils.isEmpty(robotInfor.getWebHook()) || StringUtils.isEmpty(robotInfor.getText())) {
    		 throw new Exception("Missing required parameters.");
    	 }
		 
    	 String webHook = robotInfor.getWebHook();
    	 String content = robotInfor.getText();
    	 String[] mentionedList = robotInfor.getMentionedList() == null ? new String[] {} : robotInfor.getMentionedList();
    	 JSONObject message = new JSONObject();
    	 message.put("msgtype", "text");
    	 JSONObject messageBody = new JSONObject();
    	 
    	 messageBody.put("mentioned_list",mentionedList);
    	 messageBody.put("content",content);
    	 message.put("text", messageBody);
    	 
		 JSONObject sendMessageRslut = EnterpriseWeChatMessageSendUtils.sendMessage(webHook, message);
		 log.info("sendTextMessage sendMessageRslut == {} ",sendMessageRslut);
		 return sendMessageRslut;
	}

	@Override
	public void sendImageMessage(RobotInforSet robotInforSet) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void sendFileMessage(RobotInforSet robotInforSet) throws Exception {
		// TODO Auto-generated method stub
		
	}

}
