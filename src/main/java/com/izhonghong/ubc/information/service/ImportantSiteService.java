package com.izhonghong.ubc.information.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izhonghong.ubc.information.entity.ImportantSite;
import com.baomidou.mybatisplus.extension.service.IService;
import com.izhonghong.ubc.information.entity.dto.ImportantSiteDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantSitePageDTO;
import com.izhonghong.ubc.information.entity.vo.ImportantSiteVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface ImportantSiteService extends IService<ImportantSite> {

    boolean batchSaveSites(List<ImportantSiteDTO> importantSiteDtos);

    IPage<ImportantSiteVO> findImportantSitePage(ImportantSitePageDTO importantSitePageDto);

}
