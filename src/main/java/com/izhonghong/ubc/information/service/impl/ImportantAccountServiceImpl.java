package com.izhonghong.ubc.information.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izhonghong.ubc.information.dao.ImportantAccountOrgRelationDao;
import com.izhonghong.ubc.information.entity.ImportantAccount;
import com.izhonghong.ubc.information.dao.ImportantAccountDao;
import com.izhonghong.ubc.information.entity.ImportantAccountOrgRelation;
import com.izhonghong.ubc.information.entity.ImportantSite;
import com.izhonghong.ubc.information.entity.dto.ImportantAccountDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantAccountPageDTO;
import com.izhonghong.ubc.information.entity.vo.ImportantAccountVO;
import com.izhonghong.ubc.information.entity.vo.ImportantSiteVO;
import com.izhonghong.ubc.information.service.ImportantAccountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izhonghong.ubc.information.service.personal.UserService;
import com.izhonghong.ubc.information.util.Assert;
import com.izhonghong.ubc.information.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
@Slf4j
@Service
public class ImportantAccountServiceImpl extends ServiceImpl<ImportantAccountDao, ImportantAccount> implements ImportantAccountService {

    @Autowired
    private ImportantAccountDao importantAccountDao;

    @Autowired
    private ImportantAccountOrgRelationDao importantAccountOrgRelationDao;

    @Autowired
    private UserService userService;

    @Override
    public boolean batchSaveAccounts(List<ImportantAccountDTO> importantAccountDtos) {
        List<ImportantAccount> importantAccountList = new ArrayList<>();
        List<String> uids = new ArrayList<>();
        for (ImportantAccountDTO accountDto : importantAccountDtos) {
            ImportantAccount importantAccount = new ImportantAccount();
            if (accountDto.getUid() == null) {
                continue;
            }
            BeanUtils.copyProperties(accountDto, importantAccount);
            uids.add(accountDto.getUid());

            importantAccountList.add(importantAccount);
        }

        Assert.require(!importantAccountList.isEmpty(), "无效信息源参数");

        QueryWrapper<ImportantAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ImportantAccount::getUid, uids);
        List<ImportantAccount> oldImportantAccountList = this.list(queryWrapper);
        Map<String, ImportantAccount> oldImportAccountMap = oldImportantAccountList.stream().collect(Collectors.toMap(ImportantAccount::getUid, importantAccount -> importantAccount));
        for (ImportantAccount importantAccount : importantAccountList) {
            ImportantAccount oldImportant = oldImportAccountMap.get(importantAccount.getUid());

            String orgIds = importantAccount.getOrgIds();
            String areaIds = importantAccount.getAreaIds();
            String industryIds = importantAccount.getIndustryIds();

            String oldOrgIds = null;
            String oldAreaIds = null;
            String oldIndustryIds = null;
            if (oldImportant != null) {
                oldOrgIds = oldImportant.getOrgIds();
                oldAreaIds = oldImportant.getAreaIds();
                oldIndustryIds = oldImportant.getIndustryIds();
            }
            orgIds = StringUtil.combinationIds(oldOrgIds, orgIds);
            areaIds = StringUtil.combinationIds(oldAreaIds, areaIds);
            industryIds = StringUtil.combinationIds(oldIndustryIds, industryIds);
            importantAccount.setOrgIds(orgIds);
            importantAccount.setAreaIds(areaIds);
            importantAccount.setIndustryIds(industryIds);
            importantAccount.setStatus(1);
        }

        int successCount = importantAccountDao.batchInsertImportantAccount(importantAccountList);
        boolean result = successCount > 0;
        if(result){
            for (ImportantAccountDTO importantAccountDto : importantAccountDtos) {
                List<String> orgIdList = Stream.of(importantAccountDto.getOrgIds().split(",")).collect(Collectors.toList());
                LambdaQueryWrapper<ImportantAccountOrgRelation> lambdaQueryWrapper=new LambdaQueryWrapper<>();
                lambdaQueryWrapper.in(ImportantAccountOrgRelation::getOrgId,orgIdList);
                lambdaQueryWrapper.eq(ImportantAccountOrgRelation::getUid,importantAccountDto.getUid());
                Set<Integer> existsOrgSet = importantAccountOrgRelationDao.selectList(lambdaQueryWrapper).stream().map(ImportantAccountOrgRelation::getOrgId).collect(Collectors.toSet());
                for (String orgIdStr : orgIdList) {
                    int orgId=Integer.parseInt(orgIdStr);
                    if(!existsOrgSet.contains(orgId)){
                        ImportantAccountOrgRelation item=new ImportantAccountOrgRelation();
                        item.setOrgId(orgId);
                        item.setUid(importantAccountDto.getUid());
                        item.setCreateTime(LocalDateTime.now());
                        importantAccountOrgRelationDao.insert(item);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public IPage<ImportantAccountVO> findImportantAccountPage(ImportantAccountPageDTO importantAccountPageDto) {
        IPage<ImportantAccount> page = new Page<>(importantAccountPageDto.getPageNo(), importantAccountPageDto.getPageSize());
        QueryWrapper<ImportantAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StringUtils.isNotBlank(importantAccountPageDto.getName()), ImportantAccount::getName, importantAccountPageDto.getName())
                .like(importantAccountPageDto.getOrgId() != null, ImportantAccount::getOrgIds, String.format(",%s,", importantAccountPageDto.getOrgId()))
                .like(importantAccountPageDto.getIndustryId() != null, ImportantAccount::getIndustryIds, String.format(",%s,", importantAccountPageDto.getIndustryId()))
                .like(importantAccountPageDto.getAreaId() != null, ImportantAccount::getAreaIds, String.format(",%s,", importantAccountPageDto.getAreaId()))
                .eq(importantAccountPageDto.getUid() != null, ImportantAccount::getUid, importantAccountPageDto.getUid())
                .eq(importantAccountPageDto.getStatus() != null, ImportantAccount::getStatus, importantAccountPageDto.getStatus())
                .eq(importantAccountPageDto.getPlatformType() != null, ImportantAccount::getPlatformType, importantAccountPageDto.getPlatformType())
                .orderByDesc(ImportantAccount::getUpdateTime);

        page = this.page(page, queryWrapper);

        List<ImportantAccount> accounts = page.getRecords();
        StringBuilder orgIds = new StringBuilder();
        StringBuilder areaIds = new StringBuilder();
        StringBuilder industryIds = new StringBuilder();
        for (ImportantAccount account : accounts) {
            orgIds.append(",").append(account.getOrgIds());
            areaIds.append(",").append(account.getAreaIds());
            industryIds.append(",").append(account.getIndustryIds());
        }

        Map<Integer, String> organizationNameMap = userService.convertBusinessIdNameMap(orgIds.toString(), 0);
        Map<Integer, String> areaNameMap = userService.convertBusinessIdNameMap(areaIds.toString(), 1);
        Map<Integer, String> industryNameMap = userService.convertBusinessIdNameMap(industryIds.toString(), 2);
        List<ImportantAccountVO> importantSiteVos = page.getRecords().stream().map(item -> {
            ImportantAccountVO importantAccountVO = null;
            try {
                importantAccountVO = new ImportantAccountVO();
                BeanUtils.copyProperties(item, importantAccountVO);
                String organizationNames = userService.convertNames(organizationNameMap, item.getOrgIds());
                String areaNames = userService.convertNames(areaNameMap, item.getAreaIds());
                String industryNames = userService.convertNames(industryNameMap, item.getIndustryIds());

                importantAccountVO.setOrganizationNames(organizationNames);
                importantAccountVO.setAreaNames(areaNames);
                importantAccountVO.setIndustryNames(industryNames);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return importantAccountVO;
        }).collect(Collectors.toList());

        log.info("账号分页数据列表：{}", importantSiteVos.size());

        IPage<ImportantAccountVO> importantAccountVoPage = new Page<>();
        importantAccountVoPage.setTotal(page.getTotal());
        importantAccountVoPage.setCurrent(page.getCurrent());
        importantAccountVoPage.setSize(page.getSize());
        importantAccountVoPage.setRecords(importantSiteVos);
        return importantAccountVoPage;
    }

}
