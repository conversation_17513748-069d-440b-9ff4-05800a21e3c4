package com.izhonghong.ubc.information.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izhonghong.ubc.information.entity.ImportantAccount;
import com.baomidou.mybatisplus.extension.service.IService;
import com.izhonghong.ubc.information.entity.dto.ImportantAccountDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantAccountPageDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantSiteDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantSitePageDTO;
import com.izhonghong.ubc.information.entity.vo.ImportantAccountVO;
import com.izhonghong.ubc.information.entity.vo.ImportantSiteVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface ImportantAccountService extends IService<ImportantAccount> {

    boolean batchSaveAccounts(List<ImportantAccountDTO> importantAccountDtos);

    IPage<ImportantAccountVO> findImportantAccountPage(ImportantAccountPageDTO importantAccountPageDto);

}
