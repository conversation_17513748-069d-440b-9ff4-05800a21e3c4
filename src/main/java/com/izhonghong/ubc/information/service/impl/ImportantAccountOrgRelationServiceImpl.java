package com.izhonghong.ubc.information.service.impl;

import com.izhonghong.ubc.information.entity.ImportantAccountOrgRelation;
import com.izhonghong.ubc.information.dao.ImportantAccountOrgRelationDao;
import com.izhonghong.ubc.information.service.ImportantAccountOrgRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <AUTHOR>
 * @since 2023-07-12
 */
@Service
public class ImportantAccountOrgRelationServiceImpl extends ServiceImpl<ImportantAccountOrgRelationDao, ImportantAccountOrgRelation> implements ImportantAccountOrgRelationService {

}
