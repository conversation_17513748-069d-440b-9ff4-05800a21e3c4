package com.izhonghong.ubc.information.service.impl.system;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izhonghong.ubc.inf.core.protocol.IzhResponseBody;
import com.izhonghong.ubc.information.constant.DataSourceConstant;
import com.izhonghong.ubc.information.dao.OperateRecordDao;
import com.izhonghong.ubc.information.entity.OperateRecord;
import com.izhonghong.ubc.information.entity.dto.OperateRecordDTO;
import com.izhonghong.ubc.information.service.system.OperateRecordService;
import com.izhonghong.ubc.information.util.ResponseUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2020-10-16
 */
@Service
@DS(DataSourceConstant.DATA_SOURCE)
public class OperateRecordServiceImpl extends ServiceImpl<OperateRecordDao, OperateRecord> implements OperateRecordService {

	@Autowired
	private OperateRecordDao operateRecordDao;
	
	@Override
	public IzhResponseBody selectOperateRecordInformation(OperateRecordDTO operateRecord) {
		QueryWrapper<OperateRecord> query = new QueryWrapper<OperateRecord>();
		query.lambda().eq(!StringUtils.isEmpty(operateRecord.getOperatorOrgId()),OperateRecord::getOperatorOrgId,operateRecord.getOperatorOrgId())
					  .between(!StringUtils.isEmpty(operateRecord.getStartTime()) && !StringUtils.isEmpty(operateRecord.getEndTime())
							  , OperateRecord::getOperateTime, operateRecord.getStartTime(), operateRecord.getEndTime())
					  .like(!StringUtils.isEmpty(operateRecord.getOperateModule()), OperateRecord::getOperateModule,operateRecord.getOperateModule())
					  .like(!StringUtils.isEmpty(operateRecord.getAddressIp()), OperateRecord::getIp,operateRecord.getAddressIp())
					  .like(!StringUtils.isEmpty(operateRecord.getOperatorName()), OperateRecord::getOperatorName,operateRecord.getOperatorName())
					  .like(!StringUtils.isEmpty(operateRecord.getDescription()), OperateRecord::getDescription,operateRecord.getDescription())
					  .orderByDesc(OperateRecord::getOperateTime);
	
		IPage<OperateRecord> page = this.page(new Page<>(operateRecord.getPageNo(),operateRecord.getPageSize()),query);
		
		return ResponseUtil.ok(page);
		
		
	
	}

	@Override
	public void saveOperateRecord(OperateRecord operateRecord) {
		operateRecordDao.saveOperateRecord(operateRecord);
	}

}
