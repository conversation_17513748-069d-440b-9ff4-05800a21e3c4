package com.izhonghong.ubc.information.service.impl.system;

import com.izhonghong.ubc.information.service.system.SystemAlertService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.izhonghong.ubc.information.constant.DataSourceConstant;
import com.izhonghong.ubc.information.dao.system.SystemAlertDao;
import com.izhonghong.ubc.information.entity.system.SystemAlert;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <AUTHOR>
 * @since 2021-10-11
 */
@Service
@DS(DataSourceConstant.DATA_SOURCE)
public class SystemAlertServiceImpl extends ServiceImpl<SystemAlertDao, SystemAlert> implements SystemAlertService {

}
