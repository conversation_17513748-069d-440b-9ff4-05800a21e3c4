package com.izhonghong.ubc.information.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.izhonghong.ubc.information.dao.ImportantSiteOrgRelationDao;
import com.izhonghong.ubc.information.elasticsearch.ElasticSearchAPI;
import com.izhonghong.ubc.information.entity.ImportantAccount;
import com.izhonghong.ubc.information.entity.ImportantSite;
import com.izhonghong.ubc.information.dao.ImportantSiteDao;
import com.izhonghong.ubc.information.entity.ImportantSiteOrgRelation;
import com.izhonghong.ubc.information.entity.dto.ImportantSiteDTO;
import com.izhonghong.ubc.information.entity.dto.ImportantSitePageDTO;
import com.izhonghong.ubc.information.entity.vo.ImportantSiteVO;
import com.izhonghong.ubc.information.service.ImportantSiteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.izhonghong.ubc.information.service.MediaService;
import com.izhonghong.ubc.information.service.personal.UserService;
import com.izhonghong.ubc.information.util.Assert;
import com.izhonghong.ubc.information.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023-07-05
 */
@Service
public class ImportantSiteServiceImpl extends ServiceImpl<ImportantSiteDao, ImportantSite> implements ImportantSiteService {

    @Autowired
    private ImportantSiteDao importantSiteDao;

    @Autowired
    private UserService userService;

    @Autowired
    private ImportantSiteOrgRelationDao importantSiteOrgRelationDao;

    @Autowired
    private ElasticSearchAPI elasticSearchAPI;

    @Autowired
    private MediaService mediaService;

    private Map<String,String> mediaType;


    @Override
    public boolean batchSaveSites(List<ImportantSiteDTO> importantSiteDtos) {
        if (mediaType == null) {
            mediaType = mediaService.mediaTypeMap();
        }

        List<ImportantSite> importantSiteList = new ArrayList<>();
        List<Integer> siteIds = new ArrayList<>();
        for (ImportantSiteDTO siteDto : importantSiteDtos) {
            ImportantSite importantSite = new ImportantSite();
            if (siteDto.getSiteId() == null) {
                continue;
            }
            BeanUtils.copyProperties(siteDto, importantSite);
            siteIds.add(siteDto.getSiteId());

            importantSiteList.add(importantSite);
        }

        Assert.require(!importantSiteList.isEmpty(), "无效信息源参数");

        QueryWrapper<ImportantSite> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ImportantSite::getSiteId, siteIds);
        List<ImportantSite> oldImportantSiteList = this.list(queryWrapper);
        Map<Integer, ImportantSite> oldImportSiteMap = oldImportantSiteList.stream().collect(Collectors.toMap(ImportantSite::getSiteId, importantSite -> importantSite));
        for (ImportantSite importantSite : importantSiteList) {
            String orgIds = importantSite.getOrgIds();
            String areaIds = importantSite.getAreaIds();
            String industryIds = importantSite.getIndustryIds();

            String oldOrgIds = null;
            String oldAreaIds = null;
            String oldIndustryIds = null;

            ImportantSite oldImportant = oldImportSiteMap.get(importantSite.getSiteId());
            if (oldImportant != null) {
                oldOrgIds = oldImportant.getOrgIds();
                oldAreaIds = oldImportant.getAreaIds();
                oldIndustryIds = oldImportant.getIndustryIds();
            } else {
                Integer siteId = importantSite.getSiteId();
                JSONObject siteJson = elasticSearchAPI.getSiteInfoById(siteId);
                importantSite.setCode(siteJson.getString("code"));
                importantSite.setCodeName(mediaType.get(importantSite.getCode()));
            }
            orgIds = StringUtil.combinationIds(oldOrgIds, orgIds);
            areaIds = StringUtil.combinationIds(oldAreaIds, areaIds);
            industryIds = StringUtil.combinationIds(oldIndustryIds, industryIds);
            importantSite.setOrgIds(orgIds);
            importantSite.setAreaIds(areaIds);
            importantSite.setIndustryIds(industryIds);
            importantSite.setStatus(1);
        }

        int successCount = importantSiteDao.batchInsertImportantSite(importantSiteList);
        boolean result = successCount > 0;
        if(result){
            for (ImportantSiteDTO importantSiteDto : importantSiteDtos) {
                List<String> orgIdList = Stream.of(importantSiteDto.getOrgIds().split(",")).collect(Collectors.toList());
                LambdaQueryWrapper<ImportantSiteOrgRelation> lambdaQueryWrapper=new LambdaQueryWrapper<>();
                lambdaQueryWrapper.in(ImportantSiteOrgRelation::getOrgId,orgIdList);
                lambdaQueryWrapper.eq(ImportantSiteOrgRelation::getSiteId,importantSiteDto.getSiteId());
                Set<Integer> existsOrgSet = importantSiteOrgRelationDao.selectList(lambdaQueryWrapper).stream().map(ImportantSiteOrgRelation::getOrgId).collect(Collectors.toSet());
                for (String orgIdStr : orgIdList) {
                    int orgId=Integer.parseInt(orgIdStr);
                    if(!existsOrgSet.contains(orgId)){
                        ImportantSiteOrgRelation item=new ImportantSiteOrgRelation();
                        item.setSiteId(importantSiteDto.getSiteId());
                        item.setOrgId(orgId);
                        item.setCreateTime(LocalDateTime.now());
                        importantSiteOrgRelationDao.insert(item);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public IPage<ImportantSiteVO> findImportantSitePage(ImportantSitePageDTO importantSitePageDto) {
        IPage<ImportantSite> page = new Page<>(importantSitePageDto.getPageNo(), importantSitePageDto.getPageSize());

        QueryWrapper<ImportantSite> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StringUtils.isNotBlank(importantSitePageDto.getName()), ImportantSite::getName, importantSitePageDto.getName())
                    .like(importantSitePageDto.getOrgId() != null, ImportantSite::getOrgIds, String.format(",%s,", importantSitePageDto.getOrgId()))
                    .like(importantSitePageDto.getIndustryId() != null, ImportantSite::getIndustryIds,  String.format(",%s,", importantSitePageDto.getIndustryId()))
                    .like(importantSitePageDto.getAreaId() != null, ImportantSite::getAreaIds, String.format(",%s,",importantSitePageDto.getAreaId()))
                    .like(StringUtils.isNotBlank(importantSitePageDto.getCode()), ImportantSite::getCode, importantSitePageDto.getCode())
                    .eq(importantSitePageDto.getSiteId() != null, ImportantSite::getSiteId, importantSitePageDto.getSiteId())
                    .eq(importantSitePageDto.getStatus() != null, ImportantSite::getStatus, importantSitePageDto.getStatus())
                    .orderByDesc(ImportantSite::getUpdateTime);
        page = this.page(page, queryWrapper);

        StringBuilder orgIds = new StringBuilder();
        StringBuilder areaIds = new StringBuilder();
        StringBuilder industryIds = new StringBuilder();
        for (ImportantSite site : page.getRecords()) {
            orgIds.append(",").append(site.getOrgIds());
            areaIds.append(",").append(site.getAreaIds());
            industryIds.append(",").append(site.getIndustryIds());
        }
        if (mediaType == null) {
            mediaType = mediaService.mediaTypeMap();
        }

        Map<Integer, String> organizationNameMap = userService.convertBusinessIdNameMap(orgIds.toString(), 0);
        Map<Integer, String> areaNameMap = userService.convertBusinessIdNameMap(areaIds.toString(), 1);
        Map<Integer, String> industryNameMap = userService.convertBusinessIdNameMap(industryIds.toString(), 2);
        List<ImportantSiteVO> importantSiteVos = page.getRecords().stream().map(item -> {
            ImportantSiteVO importantSiteVo = new ImportantSiteVO();
            BeanUtils.copyProperties(item, importantSiteVo);
            String organizationNames = userService.convertNames(organizationNameMap, item.getOrgIds());
            String areaNames = userService.convertNames(areaNameMap, item.getAreaIds());
            String industryNames = userService.convertNames(industryNameMap, item.getIndustryIds());

            importantSiteVo.setOrganizationNames(organizationNames);
            importantSiteVo.setAreaNames(areaNames);
            importantSiteVo.setIndustryNames(industryNames);

            importantSiteVo.setCodeName(mediaType.get(importantSiteVo.getCode()));
            return importantSiteVo;
        }).collect(Collectors.toList());

        IPage<ImportantSiteVO> importantSiteVoPage = new Page<>();
        importantSiteVoPage.setTotal(page.getTotal());
        importantSiteVoPage.setCurrent(page.getCurrent());
        importantSiteVoPage.setSize(page.getSize());
        importantSiteVoPage.setRecords(importantSiteVos);
        return importantSiteVoPage;
    }


    private String convertNames(Map<Integer, String> idNameMap, String ids) {
        if (org.apache.commons.lang.StringUtils.isBlank(ids)) {
            return "";
        }
        List<String> names = new ArrayList<>();
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            String name = idNameMap.get(id);
            if (StringUtils.isNotBlank(name)) {
                names.add(name);
            }
        }
        return names.stream().collect(Collectors.joining(","));
    }
}
