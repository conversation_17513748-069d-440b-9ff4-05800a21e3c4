package com.izhonghong.ubc.information.service.system;

import com.izhonghong.ubc.inf.core.protocol.IzhResponseBody;
import com.izhonghong.ubc.information.entity.OperateRecord;
import com.izhonghong.ubc.information.entity.dto.OperateRecordDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @since 2020-10-16
 */
public interface OperateRecordService extends IService<OperateRecord> {

	/**
	 * 综合操作日志查询
	 * */
	IzhResponseBody selectOperateRecordInformation(OperateRecordDTO operateRecord);
	
	public void saveOperateRecord(OperateRecord operateRecord);

}
