package com.izhonghong.ubc.information.service.abstracts;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.WeMediaDeleteDTO;

/***
 *信息源的接口访问
 * <AUTHOR>
 * ***/
public abstract class AbstractsInformationSourceDataService {

	/***
	 * 数据 的综合查询
	 *
	 * ***/
	public abstract JSONObject search(InformationSourceBean informationSourceBean) throws Exception;


	/**
	 * 新增或修改
	 * **/
	public abstract JSONObject saveOrUpdate(InformationSourceBean informationSourceBean) throws Exception;

	/**
	 *删除
	 * **/
	public abstract JSONObject delete(InformationSourceBean informationSourceBean) throws Exception;


	/**
	 *
	 * 根据id查询
	 * ***/
	public abstract JSONObject searchById(InformationSourceBean informationSourceBean) throws Exception;

	/**
	 *
	 * 媒体类型查询
	 * ***/
	public abstract JSONObject searchMediaType(InformationSourceBean informationSourceBean) throws Exception;



	/**
	 *
	 * 媒体类型查询
	 * ***/
	public abstract JSONObject searchInformationSource(InformationSourceBean informationSourceBean) throws Exception;

	/**
	 * 搜索信息源 - 支持POST请求和JSON请求体
	 * @param informationSourceBean 信息源基础配置（包含serviceUrl）
	 * @param requestBody JSON请求体，包含condition和paginator
	 * @return 搜索结果
	 * @throws Exception 请求异常
	 */
	public JSONObject searchInformationSource(InformationSourceBean informationSourceBean, JSONObject requestBody) throws Exception {
		// 默认实现，子类可以重写
		throw new UnsupportedOperationException("此方法需要在子类中实现");
	}


	public abstract JSONObject weMediaSaveOrUpdate(InformationSourceBean informationSourceBean) throws Exception;

	public abstract JSONObject deleteWeMedia(String apiUrl, WeMediaDeleteDTO mediaDeleteDTO) throws Exception;
}
