package com.izhonghong.ubc.information.service;

import java.io.InputStream;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.personal.User;

public interface MediaService {
	
	public JSONObject importUpdateBatch(String name,InputStream stream,User user,Integer mediaType);

	public Map<String,String> mediaTypeMap();

	public Map<String, String> mediaType();
}
