package com.izhonghong.ubc.information.service.impl.abstracts;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.common.enums.SourceTypeMenu;
import com.izhonghong.ubc.information.constant.SystemModuleEnum;
import com.izhonghong.ubc.information.elasticsearch.ElasticSearchAPI;
import com.izhonghong.ubc.information.entity.MediaLibraryInformationSourceBean;
import com.izhonghong.ubc.information.entity.OperationLog;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.MediaLibraryDTO;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.entity.vo.ErrorMessage;
import com.izhonghong.ubc.information.entity.vo.MediaTypeVO;
import com.izhonghong.ubc.information.service.AreaInfoService;
import com.izhonghong.ubc.information.service.MediaService;
import com.izhonghong.ubc.information.service.OperationLogService;
import com.izhonghong.ubc.information.util.StringUtil;
import com.izhonghong.ubc.information.util.components.SpiderApi;
import com.izhonghong.ubc.information.util.components.SystemAlertUtils;

import cn.cnhon.util.MD5;
import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class MediaServiceImpl implements MediaService{

	@Autowired
	private SystemAlertUtils systemAlertUtils;

	@Autowired
	private ElasticSearchAPI elasticSearchApi;

	@Autowired
	private AreaInfoService areaInfoService;

	@Autowired
    private SpiderApi spiderApi;

	@Autowired
    private OperationLogService operationLogService;


	@Override
	public JSONObject importUpdateBatch(String name, InputStream stream, User user,Integer mediaType) {

		JSONObject resultJson = new JSONObject();
		List<ErrorMessage> reslut = new ArrayList<>();
		List<MediaBaseDTO> base = analysisExcel(name, stream, reslut,mediaType(),mediaType);

		boolean flage = false;
		if(StringUtil.isListNull(reslut)&& !StringUtil.isListNull(base)) {
			if(mediaType == 1) {
				flage = saveMediaLibrary(base);
			}else {
				flage = saveWeMedia(base);

			}
			saveOperationLog(base, user);
		}else if(StringUtil.isListNull(reslut)) {
			ErrorMessage error = new ErrorMessage();
			error.setErrorLoc("空文件");
			error.setErrorMessage("空文件，请核对文件");
			reslut.add(error);
		}
		resultJson.put("messagelist",reslut);
		resultJson.put("flage",flage);

		return resultJson;
	}



	 protected void saveOperationLog(List<MediaBaseDTO> list,User user) {
	    	try {
	    		for (int i = 0; i < list.size(); i++) {
	    			MediaBaseDTO dto = list.get(i);
	    			OperationLog log = new OperationLog();
			    	log.setCreatedAt(new Date());
			    	log.setOperatorName(user.getLoginName());
			    	log.setOperatorId(user.getId());
			    	log.setOperatorUsertype(user.getType());
			    	log.setOperateModule("账号的批量导入");
			    	String uid = StringUtils.isEmpty(dto.getUid()) ?MD5.encode(dto.getWeb_url()):dto.getUid();
			    	log.setUuid(uid);
			    	log.setPerRequParam(dto.toString());
			    	operationLogService.save(log);
				}

	    	}catch (Exception e) {
	    		systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_GLOBAL, "操作日志保存异常  \n"+e);
			}

		}
	public boolean saveMediaLibrary(List<MediaBaseDTO> base) {
		MediaLibraryInformationSourceBean mediaLibraryInformationSourceBean = new MediaLibraryInformationSourceBean();
		mediaLibraryInformationSourceBean.setMeidaList(base);
		try {
		  JSONObject saveMediaLibraryResult = elasticSearchApi.saveOrUpdate(mediaLibraryInformationSourceBean);
		  return true;
		}catch (Exception e) {
			log.error("MediaServiceImpl saveMediaLibrary error."+e);
		}
		return false;
	}

	public boolean saveWeMedia(List<MediaBaseDTO> base) {
		 List<MediaBaseDTO> wechatMeidaList = base.stream().filter(t -> SourceTypeMenu.WE_CHAT.getValue().equals(t.getSourceType())).collect(Collectors.toList());

		 List<MediaBaseDTO> weiboMeidaList = base.stream().filter(t -> SourceTypeMenu.WEI_BO.getValue().equals(t.getSourceType())).collect(Collectors.toList());

		 List<MediaBaseDTO> toutiaoMeidaList =base.stream().filter(t -> SourceTypeMenu.TOU_TIAO.getValue().equals(t.getSourceType())).collect(Collectors.toList());

		 try {
			 WeMediaInformationSourceBean informationSourceBean = new WeMediaInformationSourceBean();
			 informationSourceBean.setWechatMeidaList(wechatMeidaList);
			 informationSourceBean.setWeiboMeidaList(weiboMeidaList);
			 informationSourceBean.setToutiaoMeidaList(toutiaoMeidaList);
			  JSONObject saveMediaLibraryResult = elasticSearchApi.weMediaBatchSaveOrUpdate(informationSourceBean);
			  return true;
			}catch (Exception e) {
				log.error("MediaServiceImpl saveMediaLibrary error."+e);
			}
			return false;
	}


	/**
	 * 表格解析
	 * **/
	private List<MediaBaseDTO> analysisExcel(String filesName,InputStream inputStream, List<ErrorMessage> reslut,Map<String,String> mediaTypeMap,Integer type) {
		List<MediaBaseDTO> patrolSourceList = new ArrayList<>();
		   try {

			   log.info("=========analysisExcel start ====================");
				Workbook ws = getWorkbook(filesName,inputStream);

				//
				inputStream.close();
				if(ws ==null) {
					throw new Exception("文件后缀异常");
				}
				//工作表对象
				Sheet sheet = ws.getSheetAt(0);
				//总行数
				int rowLength = sheet.getLastRowNum();
				//工作表的列
				Row row = sheet.getRow(0);
				//总列数
				int colLength = row.getLastCellNum();
				//判断表头
				row = sheet.getRow(2);
				ErrorMessage heetError = new ErrorMessage();
				heetError.setErrorLoc("表头");
				if(row == null) {
					heetError.setErrorMessage("无法确定表头内容");
					reslut.add(heetError);
					return patrolSourceList;
				}

				//得到指定的单元格
				Cell cell = row.getCell(0);
				if(cell == null) {
					heetError.setErrorMessage("无法确定表头内容");
					reslut.add(heetError);
					return patrolSourceList;
				}
				String tableValue = cell.getStringCellValue();
				if(StringUtils.isEmpty(tableValue)) {
					heetError.setErrorMessage("第一列表头为空");
					reslut.add(heetError);
					return patrolSourceList;
				}else {
					if(type == 1) {
						if(!"主办单位".equals(tableValue)) {
							heetError.setErrorMessage("非媒体库导入模板");
							reslut.add(heetError);
							return patrolSourceList;
						}
					}else {
						if(!"平台类型".equals(tableValue)) {
							heetError.setErrorMessage("非自媒体号导入模板");
							reslut.add(heetError);
							return patrolSourceList;
						}
					}
				}

				Map<String, Integer> distinctMap = new HashMap<>(rowLength);

				for (int i = 3; i < rowLength+1; i++) {
					String errorMessage = "";
					MediaLibraryDTO media = new MediaLibraryDTO();
					ErrorMessage error = new ErrorMessage();
				    boolean isNotNull = true;
				    error.setErrorLoc("第 "+(1+i)+" 行");
					row = sheet.getRow(i);
					log.info("row i===>{} value==> {}",i,row);
					if(row == null ){
						errorMessage += "为空";
						continue;
					}
					cell = row.getCell(0);//获取平台
					if(cell != null) {
						String cellValue = cell.getStringCellValue();
						if(type == 1) {
							if(!StringUtils.isEmpty(cellValue) && !StringUtil.checkSourceName(cellValue)) {
								isNotNull = false;
								errorMessage += "主办单位名字不规范";
							}
							media.setOrganizer(cellValue);
						}else {
							if(!StringUtils.isEmpty(cellValue)) {
								if(!SourceTypeMenu.checkType(cellValue)) {
									isNotNull = false;
									errorMessage += "超出平台类型范围";
								}else {
									media.setSourceType(cellValue);
								}
							}else {
								isNotNull = false;
								errorMessage += "平台类型为空";
							}


						}
					}else {
						if(type == 1) {
							isNotNull = false;
							errorMessage += "主办单位为空";
						}else {
							isNotNull = false;
							errorMessage += "平台类型为空";

						}
					}


					cell = row.getCell(1);//获取媒体名称
					if (cell != null) {

						cell.setCellType(CellType.STRING);
						String mediaName = cell.getStringCellValue();
						if(StringUtil.isEmpty(mediaName)) {
							errorMessage += " 第二列 空";
							isNotNull = false;
						}else {
							mediaName = mediaName.trim();
							if (type == 1) {
								if (distinctMap.containsKey(mediaName)) {
									continue;
								} else {
									distinctMap.put(mediaName, 1);
								}
							} else {
								if (distinctMap.containsKey(media.getSourceType() + "-" + mediaName)) {
									continue;
								} else {
									distinctMap.put(media.getSourceType() + "-" + mediaName, 1);
								}
							}
							if(mediaName.length() > 30) {
								errorMessage += " 第二列 名称过长";
								isNotNull = false;
							}
							if(StringUtil.checkSourceName(mediaName)) {
								media.setName(mediaName);
							}else {
								errorMessage += " 第二列 名称格式不对";
								isNotNull = false;
							}
						}
					}else {
						isNotNull = false;
						errorMessage += " 第二列空";
					}



					cell = row.getCell(2);//获取网站地址
					if (cell != null) {
						cell.setCellType(CellType.STRING);
						String url = cell.getStringCellValue();
						if(StringUtil.isEmpty(url)) {
							errorMessage += " 第三列空";
							isNotNull = false;
						}else {
							if(!StringUtil.isHttpUrl(url)){
								errorMessage += "第三列网站地址不对";
								isNotNull = false;
							}else {
								if(url.length() > 512) {
									errorMessage += "第三列网站地址过长";
									isNotNull = false;
								}
								if(type != 1) {
									Integer articleType = StringUtil.getUrlType(url);
									if(articleType == null) {
										errorMessage += "链接不在系统解析范围";
										isNotNull = false;
									}else {
										String uid = selectUidByUrl(url,articleType);
										if(!StringUtil.isEmpty(uid)) {
											media.setUid(uid);
										}else {
											errorMessage += "无法获取uid";
											isNotNull = false;
										}
									}
								}
								media.setHomeUrl(url);
								media.setWeb_url(url);
							}

						}
					}else{
						isNotNull = false;
						errorMessage += " 第三列空";
					}


					cell = row.getCell(3);//获取媒体区域
					if (cell != null) {
						cell.setCellType(CellType.STRING);
						String area = cell.getStringCellValue();
						if(StringUtil.isEmpty(area)) {
							if(type != 1) {
								errorMessage += "第四列空";
								isNotNull = false;
							}
						}else {
							if(areaInfoService.checkAreaName(area)) {
								// MediaLibraryInformationSourceBean mediaLibraryInformationSourceBean = new MediaLibraryInformationSourceBean();
								media.setArea(area);
							}/*else {
								isNotNull = false;
								errorMessage += "地区不存在";
							}*/
						}
					}else{
						if(type != 1) {
							isNotNull = false;
							errorMessage += "第四列"+"空";
						}
					}

					//后四列从最后往前找

					cell = row.getCell(7);//
					String mediaName = "";
					if(cell != null) {
						mediaName = cell.getStringCellValue();
					}

					if (StringUtils.isEmpty(mediaName)) {
						cell = row.getCell(6);
						if(cell != null) {
						   mediaName = cell.getStringCellValue();
						}
					}

					if (StringUtils.isEmpty(mediaName)) {
						cell = row.getCell(5);
						if(cell != null) {
						   mediaName = cell.getStringCellValue();
						}
					}

//					if (StringUtils.isEmpty(mediaName)) {
//
//						cell = row.getCell(4);
//						if(cell != null) {
//							mediaName = cell.getStringCellValue();
//						}
//					}

//					String mediainfor = getMedieinfor(row);

                    if(!StringUtils.isEmpty(mediaName)) {
                    	media.setMedia_type(mediaName);
                    	media.setAccountType(mediaName);
                    	String code = mediaTypeMap.get(mediaName);
                    	media.setAccountLevel(mediaName);
                    	if(code != null) {
                    	  media.setCode(code);

                    	}else {
                    		isNotNull = false;
     						errorMessage += "没有"+mediaName+"类型";
                    	}

                     }else {
                    	 isNotNull = false;
  						errorMessage += "媒体类型不能为空";
                     }


					cell = row.getCell(8);
					String refreshType = "";
					if(cell != null) {
						refreshType = cell.getStringCellValue();
					}
					if (!StringUtils.isEmpty(refreshType)) {
						final String commonlyUsed = "常用";
						final String maintenance = "维护";
						int type2 = commonlyUsed.equals(refreshType) ? 0 : (maintenance.equals(refreshType) ? 1 : -1);
						media.setRefreshType(type2);
					}

					if (isNotNull) {
						patrolSourceList.add(media);
					}else {
						error.setErrorMessage(errorMessage);
						reslut.add(error);
					}
				}
			log.info("=========analysisExcel end ====================");
		} catch (Exception e) {
			ErrorMessage error = new ErrorMessage();
			error.setErrorLoc("解析");
			error.setErrorMessage("解析异常了"+e.getMessage());
			reslut.add(error);
			log.error("MediaServiceImpl analysisExcel error{}",e);
			systemAlertUtils.warnInformation(SystemModuleEnum.OPERATE_MODULE_MEDIA, "媒体信息源的批量导入异常"+e);
			e.printStackTrace();
		}

		return patrolSourceList;
	}

	public String getMedieinfor(Row row) {
		Cell cell = row.getCell(4);
		String mediaInfor = "";
		if(cell != null) {
			String value = cell.getStringCellValue();
			if(!StringUtils.isEmpty(value)) {
				mediaInfor = value;
			}

		}
		cell = row.getCell(5);
		if(cell != null) {
			String value = cell.getStringCellValue();
			if(!StringUtils.isEmpty(value)) {
				if(StringUtils.isEmpty(mediaInfor)) {
				    mediaInfor = value;
				}else {
					mediaInfor = mediaInfor+":"+value;
				}
			}

		}

		cell = row.getCell(6);
		if(cell != null) {
			String value = cell.getStringCellValue();
			if(!StringUtils.isEmpty(value)) {
				if(StringUtils.isEmpty(mediaInfor)) {
				    mediaInfor = value;
				}else {
					mediaInfor = mediaInfor+":"+value;
				}
			}

		}

		cell = row.getCell(7);
		if(cell != null) {
			String value = cell.getStringCellValue();
			if(!StringUtils.isEmpty(value)) {
				if(StringUtils.isEmpty(mediaInfor)) {
				    mediaInfor = value;
				}else {
					mediaInfor = mediaInfor+":"+value;
				}
			}

		}

		return mediaInfor;

	}
	public String selectUidByUrl(String url,Integer type) {
		   String uuid = "";
		   JSONObject analysisUrl = spiderApi.fastCrawlArticle(url,type,"");
           if ("200".equals(analysisUrl.getString("status"))) {
            	JSONArray jsonArray = analysisUrl.getJSONArray("data");
            	if(jsonArray != null && jsonArray.size() > 0) {
            		JSONObject json = (JSONObject) jsonArray.get(0);
            		if(json != null) {
            			uuid = json.getString("uid");
            		}
            	}

           }
           return uuid;
	}

	private Workbook getWorkbook(String filesName, InputStream is) throws IOException {
        return filesName.endsWith(".xlsx") ? new XSSFWorkbook(is) : filesName.endsWith(".xls") ? new HSSFWorkbook(is) : null;
    }

	@Override
	public Map<String, String> mediaTypeMap() {
		JSONObject result = elasticSearchApi.selectMediaTypeTree(new MediaLibraryDTO());
		List<MediaTypeVO> list = JSON.parseArray(result.getString("data"),MediaTypeVO.class);
		Map<String, String> mediaTypeMap = list.stream().collect(Collectors.toMap(MediaTypeVO::getCode,MediaTypeVO::getName, (key1, key2) -> key1));
		return mediaTypeMap;
	}

	@Override
	public Map<String,String> mediaType() {
		JSONObject result = elasticSearchApi.selectMediaTypeTree(new MediaLibraryDTO());
		List<MediaTypeVO> list = JSON.parseArray(result.getString("data"),MediaTypeVO.class);
		Map<String,String> mediaTypeMap = list.stream().collect(Collectors.toMap(MediaTypeVO::getName,MediaTypeVO::getCode, (key1, key2) -> key1));
		return mediaTypeMap;
	}

}
