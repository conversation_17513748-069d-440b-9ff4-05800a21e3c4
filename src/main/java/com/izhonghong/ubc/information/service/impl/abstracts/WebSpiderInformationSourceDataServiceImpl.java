package com.izhonghong.ubc.information.service.impl.abstracts;

import javax.management.openmbean.InvalidKeyException;

import com.alibaba.fastjson.JSONArray;
import com.izhonghong.ubc.information.common.annotation.Json;
import com.izhonghong.ubc.information.entity.dto.MediaBaseDTO;
import com.izhonghong.ubc.information.entity.dto.WeMediaDeleteDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izhonghong.ubc.information.entity.InformationSourceBean;
import com.izhonghong.ubc.information.entity.MediaLibraryInformationSourceBean;
import com.izhonghong.ubc.information.entity.WeMediaInformationSourceBean;
import com.izhonghong.ubc.information.service.WebSpiderInformationSourceDataService;
import com.izhonghong.ubc.information.util.components.ISearchDataAccess;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Service
public class WebSpiderInformationSourceDataServiceImpl extends WebSpiderInformationSourceDataService{

	@Override
	public JSONObject search(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postAndHasData(informationSourceBean.getServiceUrl(),informationSourceBean.paramBody());
		return result;
	}

	@Override
	public JSONObject saveOrUpdate(InformationSourceBean informationSourceBean) throws Exception {
		MediaLibraryInformationSourceBean bean = (MediaLibraryInformationSourceBean) informationSourceBean;
		isNotNull(StringUtils.isEmpty(bean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postJson(bean.getServiceUrl(),bean.getMediaLibraryList());
		return result;
	}

	@Override
	public JSONObject delete(InformationSourceBean informationSourceBean) throws Exception {
		MediaLibraryInformationSourceBean bean = (MediaLibraryInformationSourceBean) informationSourceBean;
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postJson(informationSourceBean.getServiceUrl(), bean.getMediaLibraryList());
		return result;
	}

	@Override
	public JSONObject searchById(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.postJson(informationSourceBean.getServiceUrl(),informationSourceBean.paramBody());
		return result;
	}


	@Override
	public JSONObject searchMediaType(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.getAndHasData(informationSourceBean.getServiceUrl());
		return result;
	}
	private void isNotNull(boolean flag){
		if (flag) {
			throw new InvalidKeyException(" Missing key parameters ");
		}
	}

	@Override
	public JSONObject searchInformationSource(InformationSourceBean informationSourceBean) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		JSONObject result  = ISearchDataAccess.getAndHasData(informationSourceBean.getServiceUrl());
 		return result;
	}

	/**
	 * 搜索信息源 - 支持POST请求和JSON请求体
	 * @param informationSourceBean 信息源基础配置（包含serviceUrl）
	 * @param requestBody JSON请求体，包含condition和paginator
	 * @return 搜索结果
	 * @throws Exception 请求异常
	 */
	@Override
	public JSONObject searchInformationSource(InformationSourceBean informationSourceBean, JSONObject requestBody) throws Exception {
		isNotNull(StringUtils.isEmpty(informationSourceBean.getServiceUrl()));
		isNotNull(requestBody == null);

		// 验证请求体结构
		validateRequestBody(requestBody);

		JSONObject result = ISearchDataAccess.postAndHasData(informationSourceBean.getServiceUrl(), requestBody);
		return result;
	}

	/**
	 * 验证请求体结构
	 * @param requestBody 请求体
	 */
	private void validateRequestBody(JSONObject requestBody) {
		if (requestBody == null) {
			throw new InvalidKeyException("请求体不能为空");
		}

		if (!requestBody.containsKey("condition")) {
			throw new InvalidKeyException("请求体必须包含condition字段");
		}

		if (!requestBody.containsKey("paginator")) {
			throw new InvalidKeyException("请求体必须包含paginator字段");
		}

		JSONObject condition = requestBody.getJSONObject("condition");
		JSONObject paginator = requestBody.getJSONObject("paginator");

		if (condition == null) {
			throw new InvalidKeyException("condition字段不能为空");
		}

		if (paginator == null) {
			throw new InvalidKeyException("paginator字段不能为空");
		}

		// 验证paginator必需字段
		if (!paginator.containsKey("from") || !paginator.containsKey("size")) {
			throw new InvalidKeyException("paginator必须包含from和size字段");
		}
	}

	@Override
	public JSONObject weMediaSaveOrUpdate(InformationSourceBean informationSourceBean) throws Exception{
		WeMediaInformationSourceBean bean = (WeMediaInformationSourceBean) informationSourceBean;
		isNotNull(StringUtils.isEmpty(bean.getServiceUrl()));
		List<MediaBaseDTO> otherMediaList = bean.getOtherMediaList();
		JSONObject jsonObject = new JSONObject();
//		JSONArray objects = new JSONArray();
//		objects.add(weMediaParamBody);
		jsonObject.put("params", otherMediaList);
		jsonObject.put("refreshPolicy",1);
		JSONObject result  = ISearchDataAccess.postJson(bean.getServiceUrl(),jsonObject);
		return result;
	}

	@Override
	public JSONObject deleteWeMedia(String apiUrl, WeMediaDeleteDTO mediaDeleteDTO) throws Exception {
		JSONObject result  = ISearchDataAccess.postJson(apiUrl, JSON.parseObject(JSON.toJSONString(mediaDeleteDTO)));
		return result;
	}
}
