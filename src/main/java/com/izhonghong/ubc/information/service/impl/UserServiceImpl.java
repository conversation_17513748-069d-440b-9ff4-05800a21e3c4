package com.izhonghong.ubc.information.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.izhonghong.ubc.ca.action.OrganizationAction;
import com.izhonghong.ubc.ca.action.UserAction;
import com.izhonghong.ubc.ca.entity.Role;
import com.izhonghong.ubc.ca.utils.HttpRequest;
import com.izhonghong.ubc.information.config.CasConfig;
import com.izhonghong.ubc.information.constant.CommonConstant;
import com.izhonghong.ubc.information.constant.DataSourceConstant;
import com.izhonghong.ubc.information.entity.personal.Org;
import com.izhonghong.ubc.information.entity.personal.User;
import com.izhonghong.ubc.information.service.personal.UserService;
import com.izhonghong.ubc.information.util.AppUserUtil;
import com.izhonghong.ubc.information.util.RequestUtil;

import com.izhonghong.ubc.information.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS(DataSourceConstant.DATA_SOURCE)
public class UserServiceImpl implements UserService {


    @Autowired
    private CasConfig casConfig;

    @Override
    public User getLoginUser(HttpServletRequest request) {
        JSONObject userJson = getLoginUserForCas(request);
        User user = JSON.parseObject(userJson.toJSONString(), User.class);
        if (user == null) {
            throw new RuntimeException("未获取到当前登录的用户信息");
        }
        return user;
    }


    /**
     * 功能描述: 从UMS获取用户列表
     *
     * @param type  5-内服，6-客户
     * @param orgId information组织ID
     * @return [userJson1, userJson2]
     * <AUTHOR>
     * @date 2019-06-15 14:52:23
     */
    @Override
    public JSONArray getUserListByUms(String token, int type, Integer orgId) {
        Objects.requireNonNull(token, "获取用户列表失败，loginName");
        Map<String, Object> params = new HashMap<>();
        List<Integer> list = new ArrayList<Integer>();
        list.add(orgId);
        UserAction action = UserAction.getInstance();

        List<User> reList = new ArrayList<User>();
        try {
            List userList = action.userInformationByOrgId(token, casConfig.getUbcCasServe(), orgId);

            for (int i = 0; i < userList.size(); i++) {

                com.izhonghong.ubc.ca.entity.User user = (com.izhonghong.ubc.ca.entity.User) userList.get(i);

                User reUser = new User();
                BeanUtils.copyProperties(reUser, user);

                reList.add(reUser);
            }

        } catch (IOException e) {

            e.printStackTrace();
        }

        JSONArray json = JSONArray.parseArray(JSON.toJSONString(reList));
        return json;
    }


    /**
     * 修改用户信息 information_v1.1
     *
     * @param
     * @return
     */
    @Override
    public JSONObject updateUserInfoByUms(String jsonStr, String token) {
        Objects.requireNonNull(token, "获取token失败");
        JSONObject json = new JSONObject();
        try {
            UserAction active = UserAction.getInstance();
            String res = active.updateUserInfor(token, casConfig.getUbcCasServe(), jsonStr);
            json.put("reslut", res);
            return json;
        } catch (IOException e) {
            throw new RuntimeException("UMS用户列表接口异常");
        }
    }


    /**
     * 获取information产品下的所有组织 information_V1.1
     *
     * @param token
     * @return
     */
    @Override
    public List getOrgListByUms(String token) {
        Objects.requireNonNull(token, "获取用户列表失败，loginName");
        OrganizationAction action = OrganizationAction.getInstance();

        List list = action.orgInformation(token, casConfig.getUbcCasServe());
//        log.info(" orgInformation  getOrgListByUMS list ===> {}",list);
        List<Org> reList = new ArrayList<Org>();
        for (int i = 0; i < list.size(); i++) {
            Org reOrg = new Org();
            com.izhonghong.ubc.ca.entity.Org org = (com.izhonghong.ubc.ca.entity.Org) list.get(i);
            BeanUtils.copyProperties(reOrg, org);

            reList.add(reOrg);
        }
//        log.info(" orgInformation  getOrgListByUMS reList ===> {}",reList);

        return reList;
    }

    /**
     * 功能描述: 从request中获取token，再从UMS获取登录用户信息
     *
     * <AUTHOR>
     * @date 2019-06-17 15:04:40
     */
    @Override
    public JSONObject getLoginUserUms(HttpServletRequest request) {
        String loginName = AppUserUtil.getLoginName();
        if (RequestUtil.isBlank(loginName)) {
            throw new RuntimeException("获取登录用户失败");
        }
        JSONObject user = getLoginUserForCas(request);
        return user;
    }

    /**
     * 功能描述: 从request中获取token，再从UMS获取登录用户信息
     *
     * <AUTHOR>
     * @date 2019-06-17 15:04:40
     */
    @Override
    public JSONObject getLoginUserForCas(HttpServletRequest request) {
        String loginName = AppUserUtil.getLoginName();
        if (RequestUtil.isBlank(loginName)) {
            throw new RuntimeException("获取登录用户失败");
        }
        UserAction activa = UserAction.getInstance();

        com.izhonghong.ubc.ca.entity.User user = activa.userLoginInformation(AppUserUtil.getToken(request), casConfig.getUbcCasServe());
        User reUser = new User();
        BeanUtils.copyProperties(reUser, user);
        JSONObject json = (JSONObject) JSON.toJSON(reUser);
        return json;
    }

    @Override
    public List getUserInforByIds(List<Integer> userids, String token) {
        UserAction activ = UserAction.getInstance();
        List list = activ.userInformationByIds(token, casConfig.getUbcCasServe(), JSONArray.parseArray(userids.toString(), Long.class));
        List<User> reList = new ArrayList<User>();

        for (int i = 0; i < list.size(); i++) {

            com.izhonghong.ubc.ca.entity.User user = (com.izhonghong.ubc.ca.entity.User) list.get(i);

            User reUser = new User();
            BeanUtils.copyProperties(reUser, user);

            reList.add(reUser);
        }

        return reList;
    }

    @Override
    public Org getOrgInforByOrgId(String token, String orgId) {
        OrganizationAction active = OrganizationAction.getInstance();
        Org reOrg = new Org();

        try {
            com.izhonghong.ubc.ca.entity.Org org = active.orgInformationById(token, casConfig.getUbcCasServe(), orgId);
            BeanUtils.copyProperties(reOrg, org);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return reOrg;
    }

    @Override
    public JSONObject getLoginUerInformation(String token) {
        UserAction active = UserAction.getInstance();
        JSONObject json;
        try {
            json = active.loginUerInformation(token, casConfig.getUbcCasServe());
            return json;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public List<User> getUerInformationByName(String string, String token) {
        UserAction active = UserAction.getInstance();
        List<User> relist = new ArrayList<User>();
        try {
            List<com.izhonghong.ubc.ca.entity.User> json = active.selectUserInforByName(token, casConfig.getUbcCasServe(), string);
            for (int i = 0; i < json.size(); i++) {

                com.izhonghong.ubc.ca.entity.User user = (com.izhonghong.ubc.ca.entity.User) json.get(i);

                User reUser = new User();
                BeanUtils.copyProperties(reUser, user);

                relist.add(reUser);
            }
//			return json;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return relist;
    }


    @Override
    public Map<String, Object> findRole(String loginName, int orgid, String token) {
        UserAction active = UserAction.getInstance();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            List<Map> mapList = active.getLoginRole(token, casConfig.getUbcCasServe());
            map = mapList.get(0);
//			return json;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public Map<Integer, Object> getLoginUserForCasByRoleId(String token, List<Long> roleIds) {
        String loginName = AppUserUtil.getLoginName();
        if (RequestUtil.isBlank(loginName)) {
            throw new RuntimeException("获取登录用户失败");
        }
        UserAction activa = UserAction.getInstance();

        List<Role> roleList = activa.userInformationByRoleIds(token, casConfig.getUbcCasServe(), roleIds);
        Map<Integer, Object> resultMap = new HashMap<>();
        for (int i = 0; i < roleList.size(); i++) {
            Role role = roleList.get(i);
            resultMap.put(role.getId(), role.getRoleName());
        }

        return resultMap;
    }

    public JSONArray getOrganizationByIds(List<Integer> orgIds) {
        HttpRequest httpRequest = new HttpRequest(String.format("%s/organization/getOrganizationByIds", casConfig.getUbcCasServe()));
        String token = String.format("Bearer %s", AppUserUtil.getCurrentToken());
        JSONArray organizationList = new JSONArray();
        try {
            httpRequest.addHeader("Authorization", token);
            String result = httpRequest.postJSON(JSON.toJSONString(orgIds)).executeAsString();
            organizationList = JSON.parseArray(result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return organizationList;
    }

    public JSONArray getAreaInfoByIds(List<Integer> areaIds) {
        HttpRequest httpRequest = new HttpRequest(String.format("%s/areaTag/getAreaInfoByIds", casConfig.getUbcCasServe()));
        String token = String.format("Bearer %s", AppUserUtil.getCurrentToken());
        JSONArray areaList = new JSONArray();
        try {
            httpRequest.addHeader("Authorization", token);
            String result = httpRequest.postJSON(JSON.toJSONString(areaIds)).executeAsString();
            JSONObject resultJson = JSON.parseObject(result);
            areaList = resultJson.getJSONArray("data");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return areaList;
    }

    public JSONArray getIndustryInfoByIds(List<Integer> industryIds) {
        HttpRequest httpRequest = new HttpRequest(String.format("%s/distributionLabel/getIndustryInfoByIds", casConfig.getUbcCasServe()));
        String token = String.format("Bearer %s", AppUserUtil.getCurrentToken());
        JSONArray industryList = new JSONArray();
        try {
            httpRequest.addHeader("Authorization", token);
            String result = httpRequest.postJSON(JSON.toJSONString(industryIds)).executeAsString();
            JSONObject resultJson = JSON.parseObject(result);
            industryList = resultJson.getJSONArray("data");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return industryList;
    }

    @Override
    public Map<Integer, String> convertBusinessIdNameMap(String ids, int type) {
        if (StringUtils.isBlank(ids)) {
            return new HashMap<>();
        }

        List<Integer> intIds = new ArrayList<>();
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            if (StringUtils.isEmpty(id)) {
                continue;
            }

            Integer newId = Convert.toInt(id, 0);
            if (newId == null || intIds.contains(newId)) {
                continue;
            }

            intIds.add(newId);
        }

        if (intIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Integer, String> idNameMap = new HashMap<>();
        JSONArray dataList;
        if (type == 1) {
            dataList = getAreaInfoByIds(intIds);
        } else if (type == 2) {
            dataList = getIndustryInfoByIds(intIds);
        } else {
            dataList = getOrganizationByIds(intIds);
        }
        log.info("data===> {}", dataList);

        for (int i = 0; i < dataList.size(); i++) {
            JSONObject itemJson = dataList.getJSONObject(i);
            Integer key = itemJson.getInteger("id");
            String value;
            if (type == 1) {
                value = itemJson.getString("name");
            } else if (type == 2) {
                value = itemJson.getString("labelName");
            } else {
                value = itemJson.getString("name");
            }
            idNameMap.put(key, value);
        }
        return idNameMap;
    }

    @Override
    public String convertNames(Map<Integer, String> idNameMap, String ids) {
        if (org.apache.commons.lang.StringUtils.isBlank(ids)) {
            return "";
        }
        List<String> names = new ArrayList<>();
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            if (StringUtils.isBlank(id)) {
                continue;
            }

            Integer idInt = Integer.valueOf(id);
            String name = idNameMap.get(idInt);
            if (org.apache.commons.lang.StringUtils.isNotBlank(name)) {
                names.add(name);
            }
        }
        return names.stream().collect(Collectors.joining(","));
    }

}
