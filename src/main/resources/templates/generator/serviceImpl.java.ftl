package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
import org.springframework.stereotype.Service;
import com.izhonghong.ubc.patroler.constant.DataSourceConstant;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * <AUTHOR>
 * @since ${date}
 */
@Service
@DS(DataSourceConstant.DATA_SOURCE_PATROLER)
<#if kotlin>
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
<#else>
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

}
</#if>
