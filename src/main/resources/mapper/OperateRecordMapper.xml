<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.izhonghong.ubc.information.dao.OperateRecordDao">

    <insert id="saveOperateRecord">
	INSERT INTO t_operate_record (
		`operate_module`,
		`operator_id`,
		`operator_orgId`,
		`operator_name`,
		`operator_usertype`,
		`operate_type`,
		`operate_time`,
		`operate_result`,
		`reason`,
		`ip`,
		`description`,
		`used_time`
		)VALUES(
		#{operateModule},
		#{operatorId},
		#{operatorOrgId},
		#{operatorName},
		#{operatorUsertype},
		#{operateType},
		NOW(),
		#{operateResult},
		#{reason},
		#{ip},
		#{description},
		#{usedTime});
    </insert>
</mapper>
