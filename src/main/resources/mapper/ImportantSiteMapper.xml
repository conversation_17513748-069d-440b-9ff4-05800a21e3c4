<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.izhonghong.ubc.information.dao.ImportantSiteDao">


    <insert id="batchInsertImportantSite" parameterType="com.izhonghong.ubc.information.entity.ImportantSite" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into t_important_site (code, `name`, org_ids, industry_ids, area_ids, create_time, update_time,
        status, site_id) values
        <foreach collection="importantSites" item="item" separator=",">
            (#{item.code}, #{item.name}, #{item.orgIds}, #{item.industryIds}, #{item.areaIds}, now(),now(),
             #{item.status}, #{item.siteId})
        </foreach>
        ON DUPLICATE key update
        org_ids = values(org_ids),
        industry_ids = values(industry_ids),
        area_ids = values(area_ids),
        update_time = now()
    </insert>



</mapper>
