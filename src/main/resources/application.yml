server:
  servlet:
    context-path: /informationSource
  port: 8084
  max-http-header-size: 102400

spring:

  #可以配置多个yml，修改active启用对应的配置
  swagger2:
    enabled: true
  application:
    name: hg-infor-source
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.2.144:8848
        username: nacos
        password: nacos
        register-enabled: false
      config:
        enabled: false
  profiles:
    active: dev
  datasource:
    dynamic:
      psy6: true
      primary: information_source
      druid:
        initial-size: 5
        in-idle: 5
        max-active: 20
        max-wait: 600000
      datasource:
        #patroler数据源
        information_source:
          #url等参数配置在对应环境配置中
          driver-class-name: com.mysql.jdbc.Driver
  #不配置静态资源位置swagger2文档无法访问
  resources:
    static-locations: classpath:/templates/,classpath:/static/,classpath:/META-INF/resources/,classpath:/META-INF/resources/webjars/
  #日期格式化
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  #文件大小限制，单个文件最多200M，整个请求最多1000M
  servlet:
    multipart:
      enabled: true
      max-file-size: 200MB
      max-request-size: 1000MB
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  type-aliases-package: com.izhonghong.ubc.patroler.entity
  global-config:
    refresh-mapper: true
    db-config:
      #主键自增
      id-type: 0
      #逻辑删除值
      logic-delete-value: 1
      logic-not-delete-value: 0
      field-strategy: not_null
  configuration:
    #下划线转驼峰
    map-underscore-to-camel-case: true
#auth2放行地址，仅用于必要的外部回调
open-path: /openapi
#patroler产品ID，用于根据产品ID获取产品下的所有组织 patroler新需求
patroler-productid: 59

