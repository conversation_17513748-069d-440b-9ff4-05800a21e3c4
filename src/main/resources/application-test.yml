#开发环境相关配置
spring:
  spring:
    freemarker:
      settings:
        classic_compatible: true
  datasource:
    dynamic:
      druid:
        connection-error-retry-attempts: 0
        break-after-acquire-failure: true
      datasource:
        #patroler数据源
        information_source:
          url: **************************************************************************************************************
          username: monitorP
          password: monitorP



springfox:
   documentation:
      swagger:
        v2:
          host: ************:8084


config:
   oauth2:
      accessTokenUri: http://*************/ums

#配置采集信息的
webcrawler:
    server:
      #定时推送的时间配置,每两天 凌晨一点执行一次
       push_timer: 0 0 1 */2 * ?
       server_url: http://**************:8091
       spider_api_server: http://*************:8709
       media_maps: {"0":"微信","1":"微博","11":"APP","3":"今日头条","4":"腾讯新闻","5":"网易新闻","6":"搜狐新闻","7":"百家号","8":"凤凰新闻"}

#配置系统共用设置的

#SQL日志
logging:
  level:
    com:
      izhonghong: DEBUG

ubc:
  cas:
    openCas: true
    ubcCasServe:  http://**************:7784
    
    
    
    
#redis配置
redis:
  server:
    hostName: *************
    #端口
    port: 6381
    #密码
    password: Izhubc123
    #客户端超时时间单位是毫秒 默认是2000
    timeout: 50000  
    #最大空闲数
    maxIdle: 300  
    #连接池的最大数据库连接数。设为0表示无限制,如果是jedis 2.4以后用redis.maxTotal
#   maxActive: 600
    #控制一个pool可分配多少个jedis实例,用来替换上面的redis.maxActive,如果是jedis 2.4以后用该属性
    maxTotal: 1000  
    #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
    maxWaitMillis: 1000  
    #连接的最小空闲时间 默认1800000毫秒(30分钟)
    minEvictableIdleTimeMillis: 300000  
    #每次释放连接的最大数目,默认3
    numTestsPerEvictionRun: 1024  
    #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
    timeBetweenEvictionRunsMillis: 30000  
    #是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
    testOnBorrow: false  
    #在空闲时检查有效性, 默认false
    testWhileIdle: true  