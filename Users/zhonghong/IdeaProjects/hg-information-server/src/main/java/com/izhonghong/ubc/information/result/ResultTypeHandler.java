@Override
public void handleReturnValue(Object returnValue, MethodParameter returnType, ModelAndViewContainer mavContainer, NativeWebRequest webRequest) throws Exception {
    mavContainer.setRequestHandled(true);
    HttpServletResponse response = webRequest.getNativeResponse(HttpServletResponse.class);
    Result result = (Result) returnValue;
    // 删除或注释掉以下代码
    // if(result.getData() == null &&  StatusCode.SUCCESS.equals(result.getCode())){
    //     Type genericParameterType = returnType.getGenericParameterType();
    //     if(genericParameterType instanceof ParameterizedType){
    //         ParameterizedType type = (ParameterizedType) genericParameterType;
    //         Type actualType = type.getActualTypeArguments()[0];
    //         Class<?> rawType;
    //         if(actualType instanceof ParameterizedType){
    //             rawType = (Class<?>) ((ParameterizedType) actualType).getRawType();
    //         }else{
    //             rawType = (Class<?>) actualType;
    //         }
    //         if(Collection.class.isAssignableFrom(rawType)){
    //             result.setData(Collections.EMPTY_LIST);
    //         }
    //     }
    // }
    HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);

    String resultType = result.getResultType();
    //json返回处理格式
    if (StringUtils.isEmpty(resultType) || !XML.equalsIgnoreCase(resultType)) {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        Json[] jsonFilters = returnType.getMethod().getAnnotationsByType(Json.class);
        String resultJson;
        if(jsonFilters != null && jsonFilters.length > 0){
            resultJson = jsonSerializer.writer(getFilterProvider(request.getRequestURI(), jsonFilters))
                    .writeValueAsString(returnValue);
        }else{
            resultJson = jsonSerializer.writeValueAsString(returnValue);
        }
        response.getWriter().write(resultJson);
    }else {
        response.setContentType(MediaType.APPLICATION_XML_VALUE + ";charset=UTF-8");
        Json[] jsonFilters = returnType.getMethod().getAnnotationsByType(Json.class);
        String resultXml;
        if(jsonFilters != null && jsonFilters.length > 0){
            resultXml = xmlSerializer.writer(getFilterProvider(request.getRequestURI(), jsonFilters))
                    .writeValueAsString(returnValue);
        }else{
            resultXml = xmlSerializer.writeValueAsString(returnValue);
        }
        response.getWriter().write(resultXml);
    }

}