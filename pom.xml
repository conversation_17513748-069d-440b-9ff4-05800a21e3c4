<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.13.RELEASE</version>
		
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.izhonghong.ubc</groupId>
	<artifactId>information-source</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>information-source</name>
	<description>information-source</description>
	<!--打war包时放开注释-->
	<packaging>jar</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<mybatis-plus-boot-starter.version>3.1.0</mybatis-plus-boot-starter.version>
		<druid.version>1.1.10</druid.version>
		<swagger.version>2.8.0</swagger.version>
		<p6spy.version>3.8.0</p6spy.version>
		<ubc.version>*******</ubc.version>
		<quartz.version>2.3.2</quartz.version>
		<jjwt.version>0.11.1</jjwt.version>
<!--        <tomcat.version>8.5.78</tomcat.version>-->
        <kotlin.version>2.2.0</kotlin.version>
    </properties>


	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
			
			   <exclusion>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
			  </exclusion>
	
		
			  <exclusion>
				<groupId>org.slf4j</groupId>
				<artifactId>log4j-over-slf4j</artifactId>
			  </exclusion>
			 
			 
			</exclusions>
		</dependency>
		
		<!-- Nacos客户端引入 -->
		<dependency>
		    <groupId>com.alibaba.cloud</groupId>
		    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>

		<dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		
		 <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
            <version>4.1.2</version>
        </dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mybatis-plus-boot-starter.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.16</version>
		</dependency>
		
      <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
        
 		<!-- Tomcat 版本升级 开始 -->
<!--		<dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-tomcat</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.tomcat.embed</groupId>-->
<!--                    <artifactId>tomcat-embed-core</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.tomcat.embed</groupId>-->
<!--                    <artifactId>tomcat-embed-el</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.tomcat.embed</groupId>-->
<!--                    <artifactId>tomcat-embed-websocket</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.tomcat</groupId>-->
<!--                    <artifactId>tomcat-annotations-api</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
            <exclusions>
            
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-annotations-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-annotations-api</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${tomcat.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
       <!-- Tomcat 版本升级 结束 -->
       
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
			<version>${mybatis-plus-boot-starter.version}</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
			<exclusions>
			  <exclusion>
				 <groupId>org.slf4j</groupId>
	             <artifactId>slf4j-api</artifactId>
			  </exclusion>
			 </exclusions>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>2.5.4</version>
		</dependency>


		<dependency>
			<groupId>com.github.tobato</groupId>
			<artifactId>fastdfs-client</artifactId>
			<version>1.26.1-RELEASE</version>
			<exclusions>
			  <exclusion>
				 <groupId>org.slf4j</groupId>
	             <artifactId>slf4j-api</artifactId>
			  </exclusion>
			 </exclusions>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${swagger.version}</version>
			<exclusions>
			  <exclusion>
				 <groupId>org.slf4j</groupId>
	             <artifactId>slf4j-api</artifactId>
			  </exclusion>
			  
			   <exclusion>
				 <groupId>org.slf4j</groupId>
				<artifactId>slf4j-jdk14</artifactId>
			  </exclusion>
			   <exclusion>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
			  </exclusion>
			  
			  <exclusion>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-nop</artifactId>
			  </exclusion>
			  
			  <exclusion>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-simple</artifactId>
			  </exclusion>
			 
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${swagger.version}</version>
		</dependency>

		 <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-bean-validators</artifactId>
            <version>${swagger.version}</version>
        </dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-generator</artifactId>
			<version>${mybatis-plus-boot-starter.version}</version>
		</dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-hibernate5</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>

		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>2.3.28</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.11.0</version>
		</dependency>

		<!--统一平台jar包-->
		<dependency>
		  <groupId>com.izhonghong</groupId>
		  <artifactId>ubc-inf-core</artifactId>
		  <version>${ubc.version}</version>
		</dependency>

		<dependency>
		  <groupId>com.izhonghong</groupId>
		  <artifactId>ubc-inf-util</artifactId>
		  <version>${ubc.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>

		<dependency>
			<groupId>com.izhonghong</groupId>
			<artifactId>ubc-authority-filter</artifactId>
			<version>1.1.0</version>
		</dependency>
        <dependency>
	         <groupId>com.izhonghong</groupId>
			  <artifactId>author-callback</artifactId>
			  <version>0.0.1-SNAPSHOT</version>
         </dependency>



		<dependency>
		    <groupId>commons-io</groupId>
		    <artifactId>commons-io</artifactId>
		    <version>2.6</version>
		</dependency>

		<dependency>
		    <groupId>org.jasypt</groupId>
		    <artifactId>jasypt</artifactId>
		    <version>1.9.2</version>
		</dependency>

		<dependency>
		 <groupId>org.apache.commons</groupId>
		 <artifactId>commons-text</artifactId>
		 <version>1.4</version>
		</dependency>

		<dependency>
		  <groupId>org.jsoup</groupId>
		  <artifactId>jsoup</artifactId>
		  <version>1.9.2</version>
		</dependency>



		<dependency>
		    <groupId>com.lowagie</groupId>
		    <artifactId>itext</artifactId>
		    <version>2.1.7</version>
		</dependency>

		<dependency>
		    <groupId>com.lowagie</groupId>
		    <artifactId>itext-rtf</artifactId>
		    <version>2.1.7</version>
		</dependency>


		<dependency>
		    <groupId>commons-fileupload</groupId>
		    <artifactId>commons-fileupload</artifactId>
		    <version>1.5</version>
		</dependency>

		<dependency>
		    <groupId>org.apache.httpcomponents</groupId>
		    <artifactId>httpclient</artifactId>
		    <version>4.3.3</version>
		</dependency>

		<dependency>
		    <groupId>org.apache.httpcomponents</groupId>
		    <artifactId>httpcore</artifactId>
		    <version>4.3.3</version>
		</dependency>

		<dependency>
			<groupId>org.docx4j</groupId>
			<artifactId>docx4j</artifactId>
			<version>6.0.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			   <exclusion>
				 <groupId>org.slf4j</groupId>
	             <artifactId>slf4j-api</artifactId>
			   </exclusion>
			   <exclusion>
				 <groupId>log4j</groupId>
			     <artifactId>log4j</artifactId>
			   </exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>3.6.3</version>
			<exclusions>
			  <exclusion>
				 <groupId>org.slf4j</groupId>
	             <artifactId>slf4j-api</artifactId>
			  </exclusion>
			 </exclusions>
		</dependency>

		<!-- Quartz定时任务 -->
		<dependency>
		     <groupId>org.quartz-scheduler</groupId>
		    	 <artifactId>quartz</artifactId>
		     <version>${quartz.version}</version>
		   
	   </dependency>

       <dependency>
          <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
             <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz-jobs</artifactId>
           <version>${quartz.version}</version>
        </dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.11.4</version>
		</dependency>


		<dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.3.8</version>
        </dependency>

		<!-- jwt -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>${jjwt.version}</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>${jjwt.version}</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>${jjwt.version}</version>
		</dependency>

		<dependency>
            <groupId>org.zhonghong</groupId>
            <artifactId>search_api7</artifactId>
            <version>7130.2</version>
        </dependency>

        <dependency>
            <groupId>org.zhonghong</groupId>
            <artifactId>search_cache_api</artifactId>
            <version>7034</version>
        </dependency>

		<dependency>
			<groupId>cn.cnhon</groupId>
			<artifactId>wordsutil</artifactId>
			<version>1.0.0</version>
		</dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>
	<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-dependencies</artifactId>
            <version>2.2.2.RELEASE</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
  </dependencyManagement>

	<build>
		<plugins>
		<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
			</plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/java</source>
                                <source>target/generated-sources/annotations</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>1.8</jvmTarget>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>

        </plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
<!--					<exclude>*.yml</exclude>-->
				</excludes>
			</resource>
		</resources>
	</build>

</project>
